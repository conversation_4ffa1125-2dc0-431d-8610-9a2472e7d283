﻿using App.Base.Middleware;
using App.Base.Repository.Interface;
using App.Base.Utilities;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Attribute;
using App.ECommerce.Units.Enums;
using AutoMapper;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class MembershipLevelController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(MembershipLevelController));
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IConfigurationsRepository<SettingSys> _configurationsRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IMapper _mapper;
    private readonly IUserRepository _userRepository;
    private readonly IMembershipLevelFlow _membershipLevelFlow;
    private readonly IPriceListFlow _priceListFlow;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;

    public MembershipLevelController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IConfigurationsRepository<SettingSys> configurationsRepository,
        IMembershipLevelRepository membershipLevelRepository,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        IMapper mapper,
        IUserRepository userRepository,
        IMembershipLevelFlow membershipLevelFlow,
        IPriceListFlow priceListFlow,
        ICalcMemberLevelFlow calcMemberLevelFlow
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _configurationsRepository = configurationsRepository;
        _membershipLevelRepository = membershipLevelRepository;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _mapper = mapper;
        _userRepository = userRepository;
        _membershipLevelFlow = membershipLevelFlow;
        _priceListFlow = priceListFlow;
        _calcMemberLevelFlow = calcMemberLevelFlow;
    }

    /// <summary>
    /// Get detail MembershipLevel. (Lấy thông tin điểm xếp hạng điểm thành viên)
    /// </summary>
    /// <param name="shopId"></param>
    /// <returns>Get member rating score information</returns>
    // GET: api/partner/MembershipLevel/GetListMembershipLevel
    [HttpGet]
    public async Task<IActionResult> GetListMembershipLevel(string shopId, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));


            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<MembershipLevel> listMembershipLevel = _membershipLevelRepository.ListMembershipShop(paging, shopId);
            List<MembershipLevelDto> listMembershipLevelDto = _mapper.Map<List<MembershipLevelDto>>(listMembershipLevel.Result);

            //LogUserEvent(_log4net, Action.Load, Status.Success, $"{RoutePrefix.PARTNER}/Membership/ListMembership", $"Partner get list Branch", null, null);
            return ResponseData(new { data = listMembershipLevelDto, skip, limit, total = listMembershipLevel.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/ListMembership",
                Message = $"Error Partner get list Membershiplevel",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/ListMembership", ex);
        }
    }

    /// <summary>
    /// Create MembershipLevel (Tạo mới Danh sách xếp hạng tích điểm)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result MembershipLevel</returns>
    // POST: api/partner/MembershipLevel/CreateMembershipLevel
    [HttpPost]
    public async Task<IActionResult> CreateMembershipLevel([FromBody] MembershipCreateDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate PointRate is in allowed percentages
            if (model.PointRate < 0 || model.PointRate > 100)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_POINT_RATE"), this.ControllerContext));
            }

            // Validate SpendingThreshold (must be greater than 0)
            if (model.SpendingThreshold < 0)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("SPENDING_THRESHOLD_INVALID"), this.ControllerContext));
            }

            var membershipLevel = new MembershipLevel
            {
                ShopId = model.ShopId,
                LevelName = model.LevelName,
                PointRate = model.PointRate,
                SpendingThreshold = model.SpendingThreshold,
                // Image = new MediaInfo
                // {
                //     Type = TypeMedia.IMAGE,
                //     Link = Constants.IsAlwaysUseS3 ? "sample/icon_shop.png" : "assets/sample/icon_shop.png"
                // },
                Created = DateTimes.Now(),
                Updated = DateTimes.Now(),
            };

            membershipLevel = _membershipLevelRepository.CreateMembershipLevel(membershipLevel);

            await _membershipLevelFlow.ActionAfterUpdateMembershipLevel(membershipLevel.LevelId);

            RequestMembershipLevel membershipLevelCreateDto = _mapper.Map<RequestMembershipLevel>(membershipLevel);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/CreateMembership",
                Message = $"Partner create Shop",
                Exception = null,
                DataObject = membershipLevelCreateDto
            });

            return ResponseData(membershipLevelCreateDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/CreateMembership",
                Message = $"Error admin create Shop",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/CreateMembership", ex, model);
        }
    }

    /// <summary>
    /// Update Shop (Cập nhật thông tin danh sách xếp hạng tích điểm)
    /// </summary>
    /// <returns>The result update membership level</returns>
    // PUT: api/partner/MembershipLevel/UpdateMembershipLevel
    [HttpPut]
    public async Task<IActionResult> UpdateMembershipLevel(MembershipEditDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            MembershipLevel? membershipLevel = _membershipLevelRepository.FindByMembershipId(model.LevelId);
            if (membershipLevel == null) return ResponseBadRequest(new CustomBadRequest(localizer("MEMBERSHIPLEVEL_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(membershipLevel.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate PointRate is in allowed percentages
            if (model.PointRate < 0 || model.PointRate > 100)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_POINT_RATE"), this.ControllerContext));
            }

            // Validate SpendingThreshold (must be greater than 0)
            if (model.SpendingThreshold < 0)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("SPENDING_THRESHOLD_INVALID"), this.ControllerContext));
            }

            // if (model.Image != null) membershipLevel.Image = model.Image;
            membershipLevel.LevelName = model.LevelName;
            membershipLevel.PointRate = model.PointRate;
            membershipLevel.SpendingThreshold = model.SpendingThreshold;
            membershipLevel = _membershipLevelRepository.UpdateMembership(membershipLevel);

            await _membershipLevelFlow.ActionAfterUpdateMembershipLevel(membershipLevel.LevelId);

            MembershipEditDto membershipLevelDto = _mapper.Map<MembershipEditDto>(membershipLevel);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/UpdateMembershipLevel",
                Message = $"Admin Update MembershipLevel",
                Exception = null,
                DataObject = null
            });

            return ResponseData(new { model.LevelId, membershipLevelDto });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/UpdateMembershipLevel",
                Message = $"Error admin update Shop",
                Exception = ex,
                DataObject = null
            });
            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/UpdateMembershipLevel", ex, model);
        }
    }

    /// <summary>
    /// Delete MembershipLevel (Xóa hạng của mục tích điểm xếp hạng rank)
    /// </summary>
    /// <param name="levelId"></param>
    /// <returns>The result delete MembershipLevel</returns>
    // DELETE: api/partner/MembershipLevel/DeleteMembershipLevel
    [HttpDelete]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteMembershipLevel(string levelId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            MembershipLevel? membershipLevel = _membershipLevelRepository.FindByMembershipId(levelId);
            if (membershipLevel == null) return ResponseBadRequest(new CustomBadRequest(localizer("MEMBERSHIPLEVEL_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(membershipLevel.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var conflictingRanks = await _priceListFlow.ValidateRankConflicts(shop.ShopId, new List<string> { membershipLevel.LevelId });
            if (conflictingRanks.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("MEMBERSHIP_LEVEL_ALREADY_EXISTS_IN_PRICE_LIST"), this.ControllerContext));
            
            await _membershipLevelFlow.DeleteMembershipLevel(membershipLevel.LevelId);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/DeleteMembershipLevel",
                Message = $"Partner delete MembershipLevel",
                Exception = null,
                DataObject = membershipLevel
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("MEMBERSHIPLEVEL_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/DeleteMembershipLevel",
                Message = $"Error Partner delete MembershipLevel",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/DeleteMembershipLevel", ex);
        }
    }

    /// <summary>
    /// Lấy thông tin tổng điểm tích điểm và tổng người dùng tích điểm.
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <returns>Thông tin tổng điểm</returns>
    // GET: api/partner/MembershipLevel/summary
    [HttpGet("summary")]
    public async Task<IActionResult> SummaryPoints([FromQuery] string shopId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var result = _membershipLevelRepository.GetSumDataUser(shopId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/summary",
                Message = $"Error Partner get SummaryPoints",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/summary", ex);
        }
    }

    /// <summary>
    /// Get ListDetailUserScore. (Lấy thông tin danh sách chi điểm người dùng)
    /// </summary>
    /// <param name = "shopId" > Shop ID</param>
    /// <param name = "search" > Search keyword</param>
    /// <param name = "skip" > Number of records to skip</param>
    /// <param name = "limit" > Number of records to take</param>
    /// <returns>Get List Detail User Score</returns>
    // GET: api/partner/MembershipLevel/user-points
    [HttpGet("userpoints")]
    public async Task<IActionResult> UserPointsHistory([FromQuery] string shopId, [FromQuery] string? search, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            // Validate
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));


            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<TransactionPointDto> listMembershipLevel = _membershipLevelRepository.ListDetailUserScore(paging, shopId, search);
            List<TransactionPointDto> listMembershipLevelDto = _mapper.Map<List<TransactionPointDto>>(listMembershipLevel.Result);

            return ResponseData(new { data = listMembershipLevel.Result, skip, limit, total = listMembershipLevel.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/userpoints",
                Message = $"Error List Detail User Score",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/user-points", ex);
        }
    }

    [HttpGet("userpoint")]
    public async Task<IActionResult> GetDetailUserPoint([FromQuery] string shopId, [FromQuery] string? userId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            // Validate
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));
            TransactionPointDto membershipLevel = _membershipLevelRepository.DetailUserScore(shopId, userId);
            TransactionPointDto membershipLevelDto = _mapper.Map<TransactionPointDto>(membershipLevel);

            return ResponseData(new { data = membershipLevel });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/userpoint/{shopId}",
                Message = $"Error List Detail User Score",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/user-points", ex);
        }
    }

    /// <summary>
    /// Cập nhật điểm của người dùng.
    /// </summary>
    /// <param name="model">Dữ liệu cập nhật điểm.</param>
    /// <returns>Kết quả cập nhật điểm.</returns>
    // PUT: api/partner/MembershipLevel/points
    [HttpPut("points")]
    public async Task<IActionResult> ModifyUserPoints([FromBody] UpdateUserPointsDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            if (model == null || string.IsNullOrWhiteSpace(model.ShopId) || string.IsNullOrWhiteSpace(model.UserId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_DATA"), this.ControllerContext));
            }

            // Validate Point based on IsAdd
            if (model.IsAdd && model.Point <= 0)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("POINT_MUST_BE_POSITIVE_FOR_ADD"), this.ControllerContext));
            }

            if (!model.IsAdd && model.Point >= 0)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("POINT_MUST_BE_NEGATIVE_FOR_DEDUCT"), this.ControllerContext));
            }

            var user = _userRepository.FindByUserId(model.UserId);
            if (user == null) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_USER_NOT_FOUND"), this.ControllerContext));

            if (!model.IsAdd && Math.Abs(model.Point) > user.Point)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("UPDATE_POINTS_FAIL_INSUFFICIENTv"), this.ControllerContext));
            }

            var objPointTransaction = new PointTransaction
            {
                ShopId = model.ShopId,
                UserId = model.UserId,
                PointsEarned = model.Point,
                IsAdditionEnabled = model.IsAdd,
                Type = model.Type,
                Note = model.Note
            };

            var result = await _calcMemberLevelFlow.UpdateUserPoint(objPointTransaction);
            if (result)
            {
                LogEvent(new EventLogDto
                {
                    RefId = partnerId,
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Update,
                    Status = LogStatusEnum.Success,
                    ActionAPI = $"{RoutePrefix.PARTNER}/Membership/points",
                    Message = $"",
                    DataObject = model
                });

                return ResponseData(new { status = true, message = localizer("UPDATE_POINTS_SUCCESS") });
            }
            else
            {
                LogEvent(new EventLogDto
                {
                    RefId = partnerId,
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Update,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.PARTNER}/Membership/points",
                    Message = $"Failed to update user points",
                    DataObject = model
                });

                return ResponseBadRequest(new CustomBadRequest(localizer("UPDATE_POINTS_FAILED"), this.ControllerContext));
            }

        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/points",
                Message = $"Error updating user points",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/points", ex, model);
        }
    }

    /// <summary>
    /// Get ListPointExchangeHistory. (Lấy thông tin danh sách Lịch sử đổi điểm)
    /// </summary>
    /// <param name="shopId">Shop ID</param>
    /// <param name="search">Search keyword</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="limit">Number of records to take</param>
    /// <returns>Get List Point Exchange History</returns>
    // GET: api/partner/MembershipLevel/exchange-history
    [HttpGet("exchangehistory")]
    public async Task<IActionResult> PointExchangeHistory(
        [FromQuery] string shopId,
        [FromQuery] string? search,
        [FromQuery] int skip = 0,
        [FromQuery] int limit = 99,
        [FromQuery] TypeTransaction type = TypeTransaction.Adjust,
        [FromQuery] string? fromDate = null,
        [FromQuery] string? toDate = null)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Convert ISO string dates to DateTime
            if (string.IsNullOrEmpty(fromDate) || !DateTime.TryParse(fromDate, out DateTime fromDateTime))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_FROM_DATE"), this.ControllerContext));
            }

            if (string.IsNullOrEmpty(toDate) || !DateTime.TryParse(toDate, out DateTime toDateTime))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TO_DATE"), this.ControllerContext));
            }

            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<PointExchangeHistoryDto> listPointExchangeHistory = _membershipLevelRepository.PointExchangeHistory(paging, shopId, type, toDateTime, fromDateTime, search);

            return ResponseData(new { data = listPointExchangeHistory.Result, skip, limit, total = listPointExchangeHistory.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/exchangehistory",
                Message = $"Error Partner get list PointExchangeHistory",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/exchange-history", ex);
        }
    }

    /// <summary>
    /// Get GetIncomeHistory. (Lấy thông tin lịch sử cộng điểm)
    /// </summary>
    /// <param name="shopId">Number of records to skip</param>
    /// <param name="userId">Number of records to skip</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="limit">Number of records to take</param>
    /// <returns>Get Income History</returns>
    // GET: api/partner/MembershipLevel/income-history
    [HttpGet("incomehistory")]
    public async Task<IActionResult> IncomeHistory(string shopId, string userId, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<PointExchangeHistoryDto> incomeHistory = _membershipLevelRepository.GetIncomeHistory(paging, shopId, userId);

            return ResponseData(new { data = incomeHistory.Result, skip, limit, total = incomeHistory.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/income-history",
                Message = $"Error Partner get income history",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/income-history", ex);
        }
    }

    /// <summary>
    /// Get GetSpendingHistory. (Lấy thông tin lịch sử trừ điểm)
    /// </summary>
    /// <param name="shopId">Number of records to skip</param>
    /// <param name="userId">Number of records to skip</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="limit">Number of records to take</param>
    /// <returns>Get Spending History</returns>
    // GET: api/partner/MembershipLevel/spending-history
    [HttpGet("spendinghistory")]
    public async Task<IActionResult> SpendingHistory(string shopId, string userId, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<PointExchangeHistoryDto> spendingHistory = _membershipLevelRepository.GetSpendingHistory(paging, shopId, userId);

            return ResponseData(new { data = spendingHistory.Result, skip, limit, total = spendingHistory.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/spending-history",
                Message = $"Error Partner get spending history",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/spending-history", ex);
        }
    }

    /// <summary>
    /// GetConfigMembership. (Lấy thông tin config thiệt lập quy tắc điểm)
    /// </summary>
    /// <param name="shopId"></param>
    /// <returns>Get data Config Membership</returns>
    // GET: api/partner/MembershipLevel/config
    [HttpGet("config")]
    public async Task<IActionResult> MembershipConfig([FromQuery] string shopId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));


            if (string.IsNullOrWhiteSpace(shopId))
            {
                return BadRequest("ShopId cannot be null or empty.");
            }

            var result = _membershipLevelRepository.GetConfigMembership(shopId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/config",
                Message = $"Error Partner get list GetConfigMembership",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/config", ex);
        }
    }

    /// <summary>
    /// EditConfigMembership. (Sửa config thiệt lập quy tắc điểm)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Edit Config Membership</returns>
    // PUT: api/partner/MembershipLevel/config
    [HttpPut("config")]
    public async Task<IActionResult> ModifyMembershipConfig([FromBody] ConfigMembershipLevelDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            ConfigMembershipLevel? configMembershipLevel = _membershipLevelRepository.FindByConfigMembershipId(model.ShopId);
            if (configMembershipLevel == null)
            {
                ConfigMembershipLevel newConfigMembershipLevel = new ConfigMembershipLevel
                {
                    EarnPoint = model.EarnPoint,
                    ValidUntil = model.ValidUntil,
                    ShopId = model.ShopId,
                    Status = model.Status

                };
                configMembershipLevel = _membershipLevelRepository.CreateConfigMembershipLevel(newConfigMembershipLevel);
            }


            try
            {
                var options = new JsonSerializerOptions
                {
                    Converters = { new JsonStringEnumConverter() },
                    PropertyNameCaseInsensitive = true
                };

                _log4net.Info($"Attempting to deserialize EarnPoint JSON: {model.EarnPoint}");
                var earnPoint = JsonConvert.DeserializeObject<EarnPointDto>(model.EarnPoint);

                if (earnPoint == null)
                {
                    _log4net.Error("Failed to deserialize EarnPoint - result is null");
                    return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_EARN_POINT_FORMAT"), this.ControllerContext));
                }

                _log4net.Info($"Successfully deserialized EarnPoint. OrderJson: {earnPoint.OrderJson != null}, RegisterJson: {earnPoint.RegisterJson != null}, ShareJson: {earnPoint.ShareJson != null}");

                // Validate RegisterJson
                if (earnPoint.RegisterJson == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("REGISTER_JSON_IS_REQUIRED"), this.ControllerContext));
                }

                if (earnPoint.RegisterJson.Rule < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("REGISTER_RULE_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }

                // Validate ShareJson
                if (earnPoint.ShareJson == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("SHARE_JSON_IS_REQUIRED"), this.ControllerContext));
                }

                if (earnPoint.ShareJson.Rule < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("SHARE_RULE_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }

                if (earnPoint.ShareJson.MaxTurns < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("SHARE_MAX_TURNS_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }

                // Validate PurchaseJson
                if (earnPoint.ShareJson.IsPurchase == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PURCHASE_JSON_IS_REQUIRED"), this.ControllerContext));
                }

                if (earnPoint.ShareJson.IsPurchase.isRequire && earnPoint.ShareJson.IsPurchase.minSpent < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PURCHASE_MIN_SPENT_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }
            }
            catch (Exception ex)
            {
                _log4net.Error($"JSON Deserialization error: {ex.Message}", ex);
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_EARN_POINT_FORMAT"), this.ControllerContext));
            }

            try
            {
                var exchangePoints = JsonConvert.DeserializeObject<ExchangePointsDto>(model.ExchangePoints);
                if (exchangePoints == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_EXCHANGE_POINTS_FORMAT"), this.ControllerContext));
                }

                if (exchangePoints.DiscountJson.Rule < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("DISCOUNT_RULE_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }

                if (!exchangePoints.DiscountJson.IsScore.IsValue && exchangePoints.DiscountJson.IsScore.Value < 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("IS_SCORE_VALUE_MUST_BE_GREATER_THAN_ZERO"), this.ControllerContext));
                }
            }
            catch (Exception)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_EXCHANGE_POINTS_FORMAT"), this.ControllerContext));
            }

            if (model.ShopId != null) configMembershipLevel.Status = model.Status;
            configMembershipLevel.ExchangePoints = model.ExchangePoints;
            configMembershipLevel.EarnPoint = model.EarnPoint;
            configMembershipLevel.ValidUntil = model.ValidUntil;

            configMembershipLevel = _membershipLevelRepository.EditConfigMembership(configMembershipLevel);
            ConfigMembershipLevelDto membershipLevelDto = _mapper.Map<ConfigMembershipLevelDto>(configMembershipLevel);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/config",
                Message = $"Partner Update ModifyMembershipConfig Success",
                Exception = null,
                DataObject = configMembershipLevel
            });

            return ResponseData(membershipLevelDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/config",
                Message = $"Error Partner Update ModifyMembershipConfig",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Membership/config", ex, model);
        }
    }

    public class UpdateImageMembershipLevelModel
    {
        [Required]
        [FromForm(Name = "LevelId")]
        [DefaultValue("")]
        public string LevelId { get; set; }

        [Required]
        [FromForm(Name = "FileUpload")]
        [FileType(10 * 1024 * 1024, new string[] { ".png", ".jpg", ".jpeg", ".jfif" })]
        public IFormFile FileUpload { get; set; }
    }
    /// <summary>
    /// Update image membershiplevel
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdateImageMembershipLevel for shop</returns>
    // POST: api/partner/MembershipLevel/UpdateImageMembershipLevel
    [HttpPost("UpdateImageMembershipLevel"), DisableRequestSizeLimit]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> UpdateImageMembershipLevel([FromForm] UpdateImageMembershipLevelModel model)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            MembershipLevel? membershipLevel = _membershipLevelRepository.FindByMembershipId(model.LevelId);
            if (membershipLevel == null) return ResponseBadRequest(new CustomBadRequest(localizer("MEMBERSHIPLEVEL_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(membershipLevel.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate file size
            if (model.FileUpload == null) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));
            if (model.FileUpload.Length == 0) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));

            // remove old file
            if (membershipLevel.Image?.Link != null)
                await S3Upload.DeleteImageS3(new List<string>() { membershipLevel.Image.Link });

            // upload new file
            var keyFile = S3Upload.SendMyFileToS3(model.FileUpload, model.FileUpload.FileName.FixFileName(), "membershipLevels").Result;

            if (!string.IsNullOrEmpty(keyFile))
            {
                membershipLevel.Image ??= new MediaInfo { Type = TypeMedia.IMAGE };
                membershipLevel.Image.Link = keyFile;
            }

            membershipLevel = _membershipLevelRepository.UpdateMembership(membershipLevel);
            MembershipEditDto membershipLevelDto = _mapper.Map<MembershipEditDto>(membershipLevel);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/UpdateImageMembershipLevel",
                Message = $"Partner update image for MembershipLevel",
                DataObject = model
            });

            return ResponseData(membershipLevelDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/Membership/UpdateImageMembershipLevel",
                Message = $"Error Partner update image for MembershipLevel",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/MembershipLevel/UpdateImageMembershipLevel", ex);
        }
    }
}

