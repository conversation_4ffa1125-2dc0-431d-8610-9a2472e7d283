﻿using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.VoucherDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_USER)]
[ApiExplorerSettings(GroupName = "user-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.User)]
public class VoucherUserController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(VoucherUserController));
    private readonly IVoucherFlow _voucherFlow;
    private readonly IUserRepository _userRepository;
    private readonly IUserGroupRepository _userGroupRepository;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;

    public VoucherUserController(
        IStringLocalizer localizer, 
        IVoucherFlow voucherFlow, 
        IUserRepository userRepository,
        IUserGroupRepository userGroupRepository, 
        ICalcMemberLevelFlow calcMemberLevelFlow
    ) : base(localizer)
    {
        _voucherFlow = voucherFlow;
        _userRepository = userRepository;
        _userGroupRepository = userGroupRepository;
        _calcMemberLevelFlow = calcMemberLevelFlow;
    }

    /// <summary>
    /// Api lấy ra list voucher trong kho của người dùng
    /// </summary>
    [HttpGet("voucherbyuser")]
    public async Task<IActionResult> GetListVoucherByUserId([FromQuery] Paging paging, [FromQuery] string shopId)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }
            var userFind = _userRepository.FindByUserId(userId);
            if (userFind == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            var res = await _voucherFlow.ListVouchersByUserId(paging, userFind.UserId, shopId);
            var result = res.Data;

            return ResponseData(new { Timestamp = DateTimes.Now(), Total = result.Total, Result = result.Result });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetListVoucherByUserId: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// Api lấy ra list voucher thoả mãn giỏ hàng
    /// </summary>
    [HttpPost("voucherbycart")]
    public async Task<IActionResult> GetListVoucherByCart([FromBody] CartDto cartDto)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }
            var userFind = _userRepository.FindByUserId(userId ?? "");
            if (userFind == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            if (cartDto == null || cartDto.ListItems == null || !cartDto.ListItems.Any())
            {
                return ResponseUnauthorized(new CustomBadRequest(localizer("CART_EMPTY"),
                    this.ControllerContext));
            }

            var res = await _voucherFlow.ListAllValidVouchersForCart(userId, cartDto);
            var eligibleVouchers = res.Data;

            return ResponseData(new { Timestamp = DateTimes.Now(), Total = eligibleVouchers.Count, Result = eligibleVouchers });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetListVoucherByCart: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// Api để người dùng thu thập voucher
    /// </summary>
    [HttpGet("Redeem")]
    public async Task<IActionResult> Redeem(string voucherCode)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }
            var userFind = _userRepository.FindByUserId(userId);
            if (userFind == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            // Gọi flow xử lý thu thập voucher
            var res = await _voucherFlow.RedeemVoucher(userId, voucherCode, userFind.Point);
            if (!res.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", res.Errors.Select(e => localizer(e))), this.ControllerContext));
            var result = res.Data;
            // Xử lý trừ điểm nếu voucher yêu cầu đổi điểm
            if (result.ExchangePoints.HasValue && result.ExchangePoints > 0)
            {
                string detail = $"Đổi điểm lấy voucher: {result.VoucherCode}";
                string note = $"Đổi điểm lấy voucher: {result.VoucherCode}";

                var objPointTransaction = new PointTransaction
                {
                    ShopId = userFind.ShopId,
                    UserId = userId,
                    PointsEarned = result.ExchangePoints.Value,
                    IsAdditionEnabled = false,
                    Type = TypeTransaction.Redeem,
                    Note = note,
                    Detail = detail
                };

                await _calcMemberLevelFlow.UpdateUserPoint(objPointTransaction);
            }

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = result.Message });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in Redeem: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpGet("voucherbyshop")]
    public async Task<IActionResult> GetSystemVouchers([FromQuery] Paging paging, [FromQuery] string shopId)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }
            var userFind = _userRepository.FindByUserId(userId ?? "");
            if (userFind == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            var res = await _voucherFlow.ListSystemVouchers(paging, userId, shopId);
            if (!res.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", res.Errors.Select(e => localizer(e))), this.ControllerContext));
            var result = res.Data;
            return ResponseData(new { Timestamp = DateTimes.Now(), Total = result.Total, Result = result.Result });
        }
        catch (Exception ex)
        {
            _log4net.Error($"GetSystemVouchers | failed: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// Api để người dùng sử dụng voucher điểm
    /// </summary>
    [HttpGet("redeem-point")]
    public async Task<IActionResult> RedeemPointVoucher(string voucherCode)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }

            var user = _userRepository.FindByUserId(userId);
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            if (string.IsNullOrEmpty(voucherCode))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("VOUCHER_CODE_REQUIRED"), this.ControllerContext));
            }

            // Gọi flow xử lý theo voucherCode (Common/Unique)
            var res = await _voucherFlow.RedeemPointVoucher(userId, user.ShopId, voucherCode);
            if (!res.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", res.Errors.Select(e => localizer(e))), this.ControllerContext));
            var result = res.Data;

            string detail = $"Nhận điểm từ voucher: {voucherCode}";
            string note = $"Nhận điểm từ voucher: {voucherCode}";

            var objPointTransaction = new PointTransaction
            {
                ShopId = user.ShopId,
                UserId = userId,
                PointsEarned = result.PointsToAward ?? 0,
                IsAdditionEnabled = true,
                Type = TypeTransaction.Redeem,
                Note = note,
                Detail = detail
            };

            await _calcMemberLevelFlow.UpdateUserPoint(objPointTransaction);

            LogEvent(new EventLogDto
            {
                RefId = userId,
                RefType = TypeFor.User,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.USER}/VoucherUser/RedeemPointVoucher",
                Message = $"User redeemed {result.PointsToAward} points from voucher {voucherCode}",
                DataObject = null
            });

            return ResponseData(new
            {
                Success = true,
                PointsAwarded = result.PointsToAward,
                NewTotalPoints = user.Point + result.PointsToAward,
                Message = $"Bạn đã nhận được {result.PointsToAward} điểm!"
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"RedeemPointVoucher | failed: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// Api để lấy thông tin chi tiết voucher
    /// </summary>
    [HttpGet("voucher")]
    public async Task<IActionResult> GetVoucher([FromQuery] string voucherId)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }

            var user = _userRepository.FindByUserId(userId);
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            if (string.IsNullOrEmpty(voucherId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("VOUCHER_ID_REQUIRED"), this.ControllerContext));
            }

            var voucher = await _voucherFlow.GetVoucherByUser(voucherId, userId);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = voucher });
        }
        catch (Exception ex)
        {
            _log4net.Error($"GetVoucherPointDetail | failed: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// Api để lấy thông tin chi tiết voucher theo mã voucher (yêu cầu authentication)
    /// Trả về thông tin chi tiết + trạng thái sở hữu của user
    /// </summary>
    [HttpGet("voucherbycode")]
    public async Task<IActionResult> GetVoucherByCode([FromQuery] string voucherCode)
    {
        try
        {
            var userId = GetUserIdAuth();
            if (string.IsNullOrEmpty(userId))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_ID_REQUIRED"), this.ControllerContext));
            }

            var user = _userRepository.FindByUserId(userId);
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            if (string.IsNullOrEmpty(voucherCode))
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("VOUCHER_CODE_REQUIRED"), this.ControllerContext));
            }

            var voucher = await _voucherFlow.GetVoucherByCode(voucherCode, userId);
            if (voucher == null)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("VOUCHER_NOT_FOUND"), this.ControllerContext));
            }

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = voucher });
        }
        catch (Exception ex)
        {
            _log4net.Error($"GetVoucherByCode | failed: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}