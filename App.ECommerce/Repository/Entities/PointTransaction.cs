using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Repository.Entities;

[JsonConverter(typeof(StringEnumConverter))]
public enum TypeTransaction
{
    Shop,
    Introduce,
    Adjust,
    Register,
    Discount,
    Redeem,
    ALL,
    PointRefund
}

public enum OptionDate
{
    Day = 1,
    Week = 2,
    Month = 3
}

[BsonIgnoreExtraElements]
public class PointTransaction
{
    #region Bảng Giao dịch tích điểm
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }

    [Display(Name = "PointTransactionId")]
    [BsonElement("PointTransactionId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string PointTransactionId { get; set; }

    [Display(Name = "ShopId")]
    [BsonElement("ShopId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ShopId { get; set; }

    [Display(Name = "UserId")]
    [BsonElement("UserId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string UserId { get; set; }

    [Display(Name = "Type")]
    [BsonElement("Type")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue(TypeTransaction.Adjust)]
    public TypeTransaction Type { get; set; }

    [Display(Name = "Detail")]
    [BsonElement("Detail")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string Detail { get; set; }

    [Display(Name = "PointsEarned")]
    [BsonElement("PointsEarned")]
    [BsonRepresentation(BsonType.Int32)]
    [DefaultValue(0)]
    public int PointsEarned { get; set; }

    [Display(Name = "Created")]
    [BsonElement("Created")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Created { get; set; } = DateTimes.Now();

    [Display(Name = "Updated")]
    [BsonElement("Updated")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Updated { get; set; } = DateTimes.Now();

    [Display(Name = "IsAdditionEnabled")]
    [BsonElement("IsAdditionEnabled")]
    [BsonRepresentation(BsonType.Boolean)]
    [DefaultValue(true)]
    public bool IsAdditionEnabled { get; set; }

    [Display(Name = "Note")]
    [BsonElement("Note")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string Note { get; set; }

    [Display(Name = "Spent")]
    [BsonElement("Spent")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long Spent { get; set; }

    [Display(Name = "OrderId")]
    [BsonElement("OrderId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string OrderId { get; set; }

    [Display(Name = "ReferrerUserId")]
    [BsonElement("ReferrerUserId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ReferrerUserId { get; set; }

    //dùng để kiểm tra xem transaction đã được hoàn điểm chưa
    [Display(Name = "IsRefund")]
    [BsonElement("IsRefund")]
    [BsonRepresentation(BsonType.Boolean)]
    [DefaultValue(false)]
    public bool IsRefund { get; set; }

    [Display(Name = "Expired")]
    [BsonElement("Expired")]
    [BsonRepresentation(BsonType.Boolean)]
    [DefaultValue(false)]
    public bool Expired { get; set; }

    [Display(Name = "ExpiredAt")]
    [BsonElement("ExpiredAt")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime? ExpiredAt { get; set; }
    #endregion
}
