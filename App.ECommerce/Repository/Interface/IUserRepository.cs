using App.Base.Repository.Interface;
using App.Base.SysSetting;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using MongoDB.Bson;
using MongoDB.Driver;
using App.ECommerce.Resource.Dtos;

namespace App.ECommerce.Repository.Interface;

public interface IUserRepository : IRefreshTokenRepository
{
    User CreateUser(User item);
    User RestoreUser(User item);
    User DeleteUser(string userId);
    PagingResult<User> ListUser(Paging paging, string? shopId = null, TypeStatus? status = null);
    User UpdateUser(User item);
    long TotalUser(TypeStatus[]? listStatus = null, DateTime? from = null, DateTime? to = null);
    User? FindByUserId(string userId);
    PagingResult<User> FindByUserIds(List<string> listIds, Paging paging);

    User? FindByUserEmail(string shopId, string email, TypeProviderLogin? provider = null, TypeStatus? status = null);
    User? FindByUserPhone(string shopId, string phonenumber, TypeProviderLogin? provider = null, TypeStatus? status = null);
    User? FindByUsername(string shopId, string username, TypeProviderLogin? provider = null, TypeStatus? status = null);
    User? FindByReferralCode(string referralCode, string shopId);
    List<string> ListUserIdsByShopId(string shopId);
    List<User> ListUserByMembershipLevelId(string levelId, string shopId);
    List<User> FindByUserIds(string userIds);
    //=== Check Email & Phone
    bool CheckEmail(string shopId, string email, TypeProviderLogin? provider = null, TypeStatus? status = null);
    bool CheckPhoneNumber(string shopId, string phonenumber, TypeProviderLogin? provider = null, TypeStatus? status = null);
    bool CheckUserName(string shopId, string userName, TypeProviderLogin? provider = null, TypeStatus? status = null);
    PagingResult<User> GetUsers(Paging paging, string partnerId, string shopId, string tagName, RequiredUserDto model);

    bool IsEmailExistOnShop(string email, string shopId, string? currentUserId = null);
    bool IsPhoneExistOnShop(string phoneNumber, string shopId, string? currentUserId = null);
    IEnumerable<User> ListAllUsers(string shopId);
    Task<User> FindUserByIdAsync(string userId);
    public PagingResult<User> GetF0Users(Paging paging, string shopId);
    public Task<(int f1Count, List<string> f1UserIds)> GetF1Users(string f0UserId);
    public Task<(int f2Count, List<string> f2UserIds)> GetF2Users(List<string> f1UserIds);
    Task<PagingResult<User>> ListUserFilteredByDate(PartnerInputDto filter, string? shopId = null);
    Task<List<User>> FindReferers(string shopId, string? user = null);
    Task<PagingResult<User>> ListExcelUser(PartnerInputDto filter, string? shopId = null);
    Task<List<User>> ListUserByUserIds(List<string> userIds);
    Task<List<User>> GetUsersByFilter(FilterDefinition<User> filter);

    Task<byte[]> ExportUserTemplate();
    Task<List<User>> CreateUsers(List<User> users);

    Task<List<User>> GetListUsersWithBirthdayToday();
    Task<byte[]> ExportListUser(List<UserDto> list);
    User? FindByZaloId(string zaloId);

    List<User> GetListUserExpireAffiliate(string shopId, double expirationYears);
    Task<User> UpdateFollowOAZalo(User obj);
    Task<User> UpdateUserPoint(string userId, int point);
}