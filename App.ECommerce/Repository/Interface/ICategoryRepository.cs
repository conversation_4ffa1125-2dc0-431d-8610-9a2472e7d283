using App.Base.Repository.Entities;
using App.Base.Resource.Model;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Enums;

public interface ICategoryRepository
{
    Task<PagingResult<Category>> ListCategoryExtend(CategoryFilterDto filter);
    Task<List<Category>> GetCategoriesByShopId(string shopId, TypeCategory categoryType);
    Task<List<CategoryDto>> GetListCategoryByTypeItem(string shopId, TypeItems type);
    Task<byte[]> ExportTemplateCategory(string shopId, TypeCategory categoryType);
    Task<byte[]> ExportListCategory(List<CategoryDto> list);
    Task<List<Category>> CreateBulk(List<Category> list);

    Task<PagingResult<Category>> ListCategory(CategoryFilterDto filter);
    Task<Category?> FindByCategoryId(string categoryId);
    Task<Category?> FindByExternalId(string shopId, string externalId, SyncServiceEnum externalSource);
    Task<Category?> FindByCategoryName(string categoryName);
    Task<List<Category>> FindByCategoryIds(List<string> listIds);
    Task<PagingResult<CategoryTree>> FindTree(CategoryFilterDto filter);
    Task<PagingResult<Category>> FindKeyword(Paging paging, string valueSearch);
    Task<Category> CreateCategory(Category item);
    Task<Category> RestoreCategory(Category item);
    Task<Category> DeleteCategory(string categoryId);
    Task<List<Category>> DeleteCategories(List<string> categoryIds);
    Task<Category> UpdateCategory(Category item);
    Task<long> TotalCategory(string shopId, TypeCategory? categoryType);
    Task<Category> UpdateImages(string shopId, string categoryId, MediaInfo image);
}