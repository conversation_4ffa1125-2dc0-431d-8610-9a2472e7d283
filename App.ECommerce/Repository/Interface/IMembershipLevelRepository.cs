using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;

namespace App.ECommerce.Repository.Interface;

public interface IMembershipLevelRepository
{
    PagingResult<MembershipLevel> ListMembershipShop(Paging paging, string shopId, string? partnerId = null, TypeActive? active = null);
    MembershipLevel CreateMembershipLevel(MembershipLevel item);
    MembershipLevel? FindByMembershipId(string levelId);
    MembershipLevel? UpdateMembership(MembershipLevel item);
    MembershipLevel DeleteMembership(string shopId);
    Object GetSumDataUser(string shopId);
    PagingResult<TransactionPointDto> ListDetailUserScore(Paging paging, string shopId, string? search, string? partnerId = null, TypeActive? active = null);
    TransactionPointDto DetailUserScore(string shopId, string userId);
    PagingResult<PointExchangeHistoryDto> PointExchangeHistory(Paging paging, string shopId, TypeTransaction type, DateTime toDate, DateTime fromDate, string? search);
    PagingResult<PointExchangeHistoryDto> GetIncomeHistory(Paging paging, string shopId, string userId);
    PagingResult<PointExchangeHistoryDto> GetSpendingHistory(Paging paging, string shopId, string userId);
    ConfigMembershipLevel? GetConfigMembership(string shopId);
    ConfigMembershipLevel EditConfigMembership(ConfigMembershipLevel model);
    ConfigMembershipLevel? FindByConfigMembershipId(string shopId);
    MembershipLevel? FindMemberShipLevelBySpendingThreshold(long spendingThreshold, string shopId);
    MembershipLevel? NextMembershipLevel(long spendingThreshold, string shopId);
    PointTransaction? FindPointTransactionBy(string shopId, string orderId = null, TypeTransaction? typeTransaction = null, string userId = null, string referrerUserId = null);
    int CountShareByUserId(string shopId, string userId);

    List<PointTransaction>? ListPointTransactionBy(PointTransactionFilterDto obj);
    Task<PointTransaction> UpdatePointTransaction(PointTransaction item);
    ConfigMembershipLevel CreateConfigMembershipLevel(ConfigMembershipLevel item);
    TransactionPointDto CalculateUserScoreDetails(User user);
}
