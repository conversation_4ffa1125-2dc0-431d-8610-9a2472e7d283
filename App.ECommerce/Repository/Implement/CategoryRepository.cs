using App.Base.Repository;
using App.Base.Repository.Entities;
using App.ECommerce.Resource.Model;
using MongoDB.Driver;
using MongoDB.Bson;
using OfficeOpenXml;
using App.ECommerce.Units.Consts;
using OfficeOpenXml.Style;
using System.Drawing;
using App.ECommerce.Resource.Dtos;
using App.Base.Repository.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.InputDtos;
using System.Linq.Expressions;
using App.Base.Utilities;
using App.ECommerce.Units.Enums;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;

public class CategoryRepository : BaseRepository, ICategoryRepository
{
    private readonly IMongoCollection<Category> _collectionCategory;
    public CategoryRepository()
    {
        _collectionCategory = _database.GetCollection<Category>($"Category");
        CreateIndexOptions options = new CreateIndexOptions();
        List<CreateIndexModel<Category>> models = new List<CreateIndexModel<Category>>();
        models.Add(new CreateIndexModel<Category>(Builders<Category>.IndexKeys.Ascending((Expression<Func<Category, object>>)(item => item.CategoryId)), options));
        models.Add(new CreateIndexModel<Category>(Builders<Category>.IndexKeys.Ascending((Expression<Func<Category, object>>)(item => item.ParentId)), options));
        this._collectionCategory.Indexes.CreateMany((IEnumerable<CreateIndexModel<Category>>)models);
    }

    public async Task<PagingResult<Category>> ListCategoryExtend(CategoryFilterDto filter)
    {
        PagingResult<Category> result = new PagingResult<Category>();
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Where(x => filter.PartnerId == null || x.PartnerId == filter.PartnerId),
            builder.Where(x => filter.ShopId == null || x.ShopId == filter.ShopId),
            builder.Where(x => filter.IsHome == null || x.IsHome == filter.IsHome),
            builder.Where(x => filter.Level == null || x.CategoryLevel == filter.Level),
            builder.Where(x => x.Publish == TypeCategoryPublish.Publish)
        };

        if (filter.ParentId == "root" || filter.ParentId == "")
            filters.Add(builder.Eq(x => x.ParentId, null));

        else if (filter.ParentId != null)
            filters.Add(builder.Eq(x => x.ParentId, filter.ParentId));

        if (filter.CategoryType != null)
            filters.Add(builder.Eq(x => x.CategoryType, filter.CategoryType));

        var filterBuilders = builder.And(filters);

        // Đếm tổng số bản ghi
        result.Total = await _collectionCategory.CountDocumentsAsync(filterBuilders);

        // Lấy dữ liệu phân trang
        result.Result = await _collectionCategory
            .Find(filterBuilders)
            .Sort($"{{CategoryPosition: 1, {filter.Paging.NameType}: {(filter.Paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(filter.Paging.PageIndex * filter.Paging.PageSize)
            .Limit(filter.Paging.PageSize)
            .ToListAsync();

        return result;
    }

    public async Task<List<Category>> GetCategoriesByShopId(string shopId, TypeCategory categoryType)
    {
        var builder = Builders<Category>.Filter;
        var filter = builder.And(
            builder.Where(x => x.ShopId == shopId),
            builder.Where(x => x.CategoryType == categoryType),
            builder.Where(x => x.Publish == TypeCategoryPublish.Publish)
        );

        return await _collectionCategory.Find(filter)
            .Sort($"{{CategoryPosition: 1, CategoryName: 1}}")
            .ToListAsync();
    }

    public async Task<List<CategoryDto>> GetListCategoryByTypeItem(string shopId, TypeItems type)
    {
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Where(x => x.ShopId == shopId),
            builder.Where(x => x.CategoryType == (TypeCategory)type),
            builder.Where(x => x.Publish == TypeCategoryPublish.Publish)
        };

        var filterBuilders = builder.And(filters);

        var projection = Builders<Category>.Projection.Expression(category => new CategoryDto
        {
            CategoryId = category.CategoryId,
            CategoryName = category.CategoryName,
            CategoryDisplayName = category.CategoryName,
        });

        return await _collectionCategory.Find(filterBuilders)
            .Project(projection)
            .ToListAsync();
    }

    public async Task<byte[]> ExportTemplateCategory(string shopId, TypeCategory categoryType)
    {
        var type = categoryType == TypeCategory.Product ? TypeItems.Product : TypeItems.Service;

        List<CategoryDto> categories = await GetListCategoryByTypeItem(shopId, type);
        var listCategory = new List<string>();
        listCategory.AddRange(categories.Select(c => c.CategoryDisplayName));
        
        using var package = new ExcelPackage();
        
        // Tạo sheet ẩn chứa danh sách categories
        var hiddenSheet = package.Workbook.Worksheets.Add("Categories");
        hiddenSheet.Hidden = OfficeOpenXml.eWorkSheetHidden.VeryHidden;

        // Thêm danh sách categories vào sheet ẩn
        for (int i = 0; i < listCategory.Count; i++)
        {
            hiddenSheet.Cells[i + 1, 1].Value = listCategory[i];
        }

        var worksheet = package.Workbook.Worksheets.Add("CategoryTemplate");
        var columns = ExportConst.HEADER_TEMPLATE_CATEGORY;

        // Thêm header
        for (int i = 0; i < columns.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = columns[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            worksheet.Column(i + 1).Width = 20;
        }

        // Data demo
        worksheet.Cells[2, 1].Value = "Đồng hồ";
        worksheet.Cells[2, 2].Value = "";
        worksheet.Cells[2, 3].Value = 100;
        worksheet.Cells[2, 4].Value = "https://example.com/image1.jpg";

        // Áp dụng style header
        using (var range = worksheet.Cells[1, 1, 1, columns.Length])
        {
            range.Style.Font.Bold = true;
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;
            range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Áp dụng border + font cho toàn bộ bảng
        using (var range = worksheet.Cells[1, 1, 2, columns.Length])
        {
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;

            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Căn giữa các cột
        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Cells[1, i, 2, i].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        }

        var dataValidation = worksheet.DataValidations.AddListValidation("B2:B1000");
        dataValidation.ShowErrorMessage = true;
        dataValidation.ErrorTitle = "Lỗi";
        dataValidation.Error = "Vui lòng chọn danh mục cha từ danh sách";
        if (listCategory.Count > 0)
            dataValidation.Formula.ExcelFormula = $"Categories!$A$1:$A${listCategory.Count}";

        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return package.GetAsByteArray();
    }
    public async Task<byte[]> ExportListCategory(List<CategoryDto> list)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");

        // Định nghĩa các header
        var headers = ExportConst.HEADER_EXPPORT_CATEGORY;

        // Tạo header
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
        }

        // Áp dụng style cho header
        var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Font.Name = "Times New Roman";
        headerRange.Style.Font.Size = 13;
        headerRange.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        headerRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Nếu không có dữ liệu, trả về file với chỉ header
        if (list == null || list.Count == 0)
        {
            for (int i = 1; i <= headers.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            return package.GetAsByteArray();
        }

        // Thêm dữ liệu
        for (int i = 0; i < list.Count; i++)
        {
            int row = i + 2; // Bắt đầu từ dòng 2 (sau header)

            worksheet.Cells[row, 1].Value = i + 1; // STT
            worksheet.Cells[row, 2].Value = list[i].CategoryName ?? ""; // Tên danh mục
            worksheet.Cells[row, 3].Value = list[i].ParentName ?? ""; // Danh mục cha
            worksheet.Cells[row, 4].Value = list[i].QuantityItems ?? 0; // Số lượng
            worksheet.Cells[row, 5].Value = list[i].CategoryPosition; // Vị trí hiển thị
            worksheet.Cells[row, 6].Value = list[i].Publish; // Trạng thái
            worksheet.Cells[row, 7].Value = list[i].Image?.Link; // Hình ảnh
        }

        // Áp dụng style cho toàn bộ bảng
        var dataRange = worksheet.Cells[1, 1, list.Count + 1, headers.Length];
        dataRange.Style.Font.Name = "Times New Roman";
        dataRange.Style.Font.Size = 13;
        dataRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Căn giữa các cột
        worksheet.Cells[2, 1, list.Count + 1, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 2, list.Count + 1, 2].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 3, list.Count + 1, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 4, list.Count + 1, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 5, list.Count + 1, 5].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 6, list.Count + 1, 6].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;

        for (int i = 1; i <= headers.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return package.GetAsByteArray();
    }
    public async Task<List<Category>> CreateBulk(List<Category> list)
    {
        if (list == null || !list.Any())
            return new List<Category>();

        await _collectionCategory.InsertManyAsync(list);

        return list;
    }

    public async Task<PagingResult<Category>> ListCategory(CategoryFilterDto filter)
    {
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Where(x => filter.PartnerId == null || x.PartnerId == filter.PartnerId),
            builder.Where(x => filter.ShopId == null || x.ShopId == filter.ShopId),
            builder.Where(x => filter.IsHome == null || x.IsHome == filter.IsHome),
            builder.Where(x => filter.Level == null || x.CategoryLevel == filter.Level)
        };

        if (filter.ParentId != null)
            filters.Add(builder.Eq(x => x.ParentId, filter.ParentId));

        if (filter.CategoryType.HasValue)
            filters.Add(builder.Eq(x => x.CategoryType, filter.CategoryType.Value));

        if (filter.Publish.HasValue)
            filters.Add(builder.Eq(x => x.Publish, filter.Publish.Value));

        if (!string.IsNullOrEmpty(filter.Paging.Search))
        {
            filters.Add(
                builder.Regex(
                    x => x.CategoryNameOrigin,
                    new MongoDB.Bson.BsonRegularExpression(
                        $@"{filter.Paging.Search.NonUnicode().ToLower().EscapeSpecialChars()}",
                        "i"
                    )
                )
            );
        }

        var filterBuilders = builder.And(filters);
        PagingResult<Category> result = new PagingResult<Category>();
        result.Total = await _collectionCategory.CountDocumentsAsync(filterBuilders);
        result.Result = await _collectionCategory
            .Find(filterBuilders)
            .Sort($"{{CategoryPosition: 1, {filter.Paging.NameType}: {(filter.Paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(filter.Paging.PageIndex * filter.Paging.PageSize)
            .Limit(filter.Paging.PageSize)
            .ToListAsync();
        return result;
    }

    public async Task<Category?> FindByCategoryId(string categoryId)
    {
        return await _collectionCategory.Find(x => x.CategoryId == categoryId).FirstOrDefaultAsync();
    }
    public async Task<Category?> FindByExternalId(string shopId, string externalId, SyncServiceEnum externalSource)
    {
        return await _collectionCategory.Find(
                    x => x.ExternalId == externalId
                    && x.ExternalSource == externalSource
                    && x.ShopId == shopId).FirstOrDefaultAsync();
    }

    public async Task<Category?> FindByCategoryName(string categoryName)
    {
        return await _collectionCategory.Find(x => x.CategoryName == categoryName).FirstOrDefaultAsync();
    }

    public async Task<List<Category>> FindByCategoryIds(List<string> listIds)
    {
        if (listIds == null || listIds.Count == 0) return new List<Category>();
        var filter = Builders<Category>.Filter.In(x => x.CategoryId, listIds);
        return await _collectionCategory.Find(filter).ToListAsync();
    }

    public async Task<PagingResult<CategoryTree>> FindTree(CategoryFilterDto filter)
    {
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Where(x => filter.PartnerId == null || x.PartnerId == filter.PartnerId),
            builder.Where(x => filter.ShopId == null || x.ShopId == filter.ShopId),
            builder.Where(x => filter.IsHome == null || x.IsHome == filter.IsHome),
            builder.Where(x => filter.Level == null || x.CategoryLevel == filter.Level)
        };

        if (filter.ParentId == "root" || filter.ParentId == "")
            filters.Add(builder.Eq(x => x.ParentId, null));

        else if (filter.ParentId != null)
            filters.Add(builder.Eq(x => x.ParentId, filter.ParentId));

        if (filter.CategoryType != null)
            filters.Add(builder.Eq(x => x.CategoryType, filter.CategoryType));

        if (filter.Publish.HasValue)
            filters.Add(builder.Eq(x => x.Publish, filter.Publish.Value));

        var filterBuilders = builder.And(filters);
        var allCategories = await _collectionCategory.Find(filterBuilders).ToListAsync();

        List<CategoryTree> BuildTree(string? parentId)
        {
            return allCategories.Where(x => x.ParentId == parentId).Select(x => new CategoryTree
            {
                CategoryId = x.CategoryId,
                ParentId = x.ParentId,
                CategoryName = x.CategoryName,
                CategoryLevel = x.CategoryLevel,
                Image = x.Image,
                CategoryDesc = x.CategoryDesc,
                ListSubCategory = BuildTree(x.CategoryId),
                Created = x.Created,
                Updated = x.Updated
            }).ToList();
        }
        var tree = BuildTree(null);
        return new PagingResult<CategoryTree> { Result = tree, Total = tree.Count };
    }

    public async Task<PagingResult<Category>> FindKeyword(Paging paging, string valueSearch)
    {
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Regex(x => x.CategoryName, new MongoDB.Bson.BsonRegularExpression(valueSearch, "i"))
        };
        var filterBuilders = builder.And(filters);
        PagingResult<Category> result = new PagingResult<Category>();
        var query = _collectionCategory.Find(filterBuilders);
        result.Total = await query.CountDocumentsAsync();
        result.Result = await query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();
        return result;
    }

    public async Task<Category> CreateCategory(Category item)
    {
        ObjectId newId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(newId).ToString();
        item.CategoryId = Guid.NewGuid().ToString();
        item.CategoryNameOrigin = item.CategoryName.NonUnicode().ToLower();
        item.Created = DateTimesEx.Now();
        item.Updated = DateTimesEx.Now();
        await this._collectionCategory.InsertOneAsync(item);

        return item;
    }

    public async Task<Category> RestoreCategory(Category item)
    {
        ObjectId newId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(newId).ToString();
        item.CategoryId = !string.IsNullOrEmpty(item.CategoryId) ? item.CategoryId : Guid.NewGuid().ToString();
        item.CategoryNameOrigin = item.CategoryName.NonUnicode().ToLower();
        item.Created = DateTimesEx.Now();
        item.Updated = DateTimesEx.Now();
        await _collectionCategory.InsertOneAsync(item);
        return item;
    }

    public async Task<Category> DeleteCategory(string categoryId)
    {
        return await _collectionCategory.FindOneAndDeleteAsync<Category>((Expression<Func<Category, bool>>)(item => item.CategoryId == categoryId));
    }

    public async Task<List<Category>> DeleteCategories(List<string> categoryIds)
    {
        if (categoryIds == null || !categoryIds.Any())
            return new List<Category>();

        var filter = Builders<Category>.Filter.In(x => x.CategoryId, categoryIds);
        var deletedCategories = await _collectionCategory.Find(filter).ToListAsync();
        
        if (deletedCategories.Any())
        {
            await _collectionCategory.DeleteManyAsync(filter);
        }

        return deletedCategories;
    }

    public async Task<Category> UpdateCategory(Category item)
    {
        var filter = Builders<Category>.Filter.Eq(x => x.CategoryId, item.CategoryId);
        var update = Builders<Category>.Update
            .Set(x => x.ParentId, item.ParentId)
            .Set(x => x.CategoryName, item.CategoryName)
            .Set(x => x.CategoryNameOrigin, item.CategoryName?.NonUnicode().ToLower())
            .Set(x => x.CategoryLevel, item.CategoryLevel)
            .Set(x => x.Image, item.Image)
            .Set(x => x.CategoryDesc, item.CategoryDesc)
            .Set(x => x.CategoryPosition, item.CategoryPosition)
            .Set(x => x.Publish, item.Publish)
            .Set(x => x.Updated, DateTime.Now);
        var options = new FindOneAndUpdateOptions<Category> { IsUpsert = false, ReturnDocument = ReturnDocument.After };
        return await _collectionCategory.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<long> TotalCategory(string shopId, TypeCategory? categoryType)
    {
        var builder = Builders<Category>.Filter;
        var filters = new List<FilterDefinition<Category>>
        {
            builder.Where(x => shopId == null || x.ShopId == shopId)
        };
        if (categoryType != null)
            filters.Add(builder.Eq(x => x.CategoryType, categoryType));
        var filterBuilders = builder.And(filters);
        return await _collectionCategory.CountDocumentsAsync(filterBuilders);
    }

    public async Task<Category> UpdateImages(string shopId, string categoryId, MediaInfo image)
    {
        var item = _collectionCategory.Find(x => x.ShopId == shopId && x.CategoryId == categoryId).FirstOrDefault();
        if (item == null) return null;

        var update = Builders<Category>.Update
            .Set("Image", image)
            .Set("Updated", DateTimes.Now());

        var filter = Builders<Category>.Filter.Eq("Id", item.Id);
        var options = new FindOneAndUpdateOptions<Category> { IsUpsert = false, ReturnDocument = ReturnDocument.After };
        return await _collectionCategory.FindOneAndUpdateAsync(filter, update, options);
    }
}
