using System.Text;
using App.Base.Repository.Implement;
using App.Base.SysSetting;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using log4net;
using MongoDB.Bson;
using MongoDB.Driver;
using OfficeOpenXml;
using static App.ECommerce.Resource.Enums.AffiliationEnum;
using System.Reflection;

namespace App.ECommerce.Repository.Implement;

public class UserRepository : RefreshTokenRepository, IUserRepository
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(UserRepository));
    private readonly IMongoCollection<User> _collectionUser;

    private readonly IShippingAddressRepository _shippingAddressRepository;
    public UserRepository(
        IShippingAddressRepository shippingAddressRepository
    ) : base()
    {
        _collectionUser = _database.GetCollection<User>($"User");

        var indexOptions = new CreateIndexOptions();
        var indexModel = new List<CreateIndexModel<User>>
        {
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.UserId), indexOptions),
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.PhoneNumber), indexOptions),
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.Email), indexOptions),
            new CreateIndexModel<User>(
                Builders<User>.IndexKeys
                    .Ascending(x => x.Status)
                    .Ascending(x => x.ShopId)
                    .Descending(x => x.Created),
                new CreateIndexOptions { Name = "status_shopid_created_index" })
        };
        _collectionUser.Indexes.CreateMany(indexModel);

        _shippingAddressRepository = shippingAddressRepository;
    }

    public User CreateUser(User item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.UserId = Guid.NewGuid().ToString();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        item.ReferralCode = item.PhoneNumber.FormatPhonePrefix0();
        _collectionUser.InsertOne(item);
        return item;
    }

    public User RestoreUser(User item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.UserId = (!string.IsNullOrEmpty(item.UserId) ? item.UserId : Guid.NewGuid().ToString());
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionUser.InsertOne(item);
        return item;
    }

    public User DeleteUser(string userId)
    {
        return _collectionUser.FindOneAndDelete(item => item.UserId == userId);
    }

    public PagingResult<User> ListUser(Paging paging, string? shopId = null, TypeStatus? typeStatus = null)
    {
        string searchQuery = ConvertPhoneNumber.NormalizePhoneNumber(paging.Search);
        PagingResult<User> result = new PagingResult<User>();
        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            Builders<User>.Filter.Where(x => typeStatus == null || x.Status == typeStatus),
            Builders<User>.Filter.Where(x => shopId == null || x.ShopId == shopId),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.PhoneNumber, new BsonRegularExpression($@"{searchQuery}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Email, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.ReferralCode, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );
        var query = _collectionUser.Find(filterBuilders);
        result.Total = query.CountDocuments();
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public User UpdateUser(User item)
    {
        User _item = _collectionUser.Find(x => x.UserId == item.UserId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<User>.Update
            .Set("UserId", item.UserId)
            .Set("ShopId", item.ShopId)
            .Set("Provider", item.Provider)
            .Set("PhoneNumber", item.PhoneNumber)
            .Set("Email", item.Email)
            .Set("Password", item.Password)
            .Set("Firstname", item.Firstname)
            .Set("Lastname", item.Lastname)
            .Set("Fullname", item.Fullname)
            .Set("Username", item.Username)
            .Set("Birthdate", item.Birthdate)
            .Set("Address", item.Address)
            .Set("Avatar", item.Avatar)
            .Set("Gender", item.Gender)
            .Set("ProvinceId", item.ProvinceId)
            .Set("ProvinceName", item.ProvinceName)
            .Set("DistrictId", item.DistrictId)
            .Set("DistrictName", item.DistrictName)
            .Set("WardId", item.WardId)
            .Set("WardName", item.WardName)
            .Set("Address", item.Address)
            .Set("Point", item.Point)
            .Set("BankName", item.BankName)
            .Set("BankAccountName", item.BankAccountName)
            .Set("BankAccountNumber", item.BankAccountNumber)
            .Set("Language", item.Language)
            .Set("ReferralCode", item.ReferralCode)
            .Set("Notes", item.Notes)
            .Set("Verified", item.Verified)
            .Set("Verification", item.Verification)
            .Set("VerificationExpires", item.VerificationExpires)
            .Set("LoginAttempts", item.LoginAttempts)
            .Set("BlockExpires", item.BlockExpires)
            .Set("ReferrerCode", item.ReferrerCode)
            .Set("Status", item.Status)
            .Set("IsZaloOA", item.IsZaloOA)
            .Set("MembershipLevelId", item.MembershipLevelId)
            .Set("ZaloId", item.ZaloId)
            .Set("ZaloIdByOA", item.ZaloIdByOA)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now())
            .Set("PaymentType", item.PaymentType)
            .Set("AffiliationStatus", item.AffiliationStatus)
            .Set("ApprovalDate", item.ApprovalDate)
            .Set("IdentityCardNumber", item.IdentityCardNumber)
            .Set("AffiliationFullName", item.AffiliationFullName)
            .Set("AffiliationEmail", item.AffiliationEmail)
            .Set("AffiliationPhoneNumber", item.AffiliationPhoneNumber)
            .Set("TaxCode", item.TaxCode)
            .Set("ParentId", item.ParentId)
            .Set("AffiliationExpireDate", item.AffiliationExpireDate);
        var filter = Builders<User>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<User> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionUser.FindOneAndUpdate(filter, update, options);
    }

    public async Task<User> UpdateFollowOAZalo(User obj)
    {
        User _obj = _collectionUser.Find(x => x.UserId == obj.UserId).FirstOrDefault();
        if (_obj == null) return null;

        var update = Builders<User>.Update
            .Set("IsZaloOA", obj.IsZaloOA);
        var filter = Builders<User>.Filter.Eq("Id", _obj.Id);
        var options = new FindOneAndUpdateOptions<User> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return await _collectionUser.FindOneAndUpdateAsync(filter, update, options);
    }

    public long TotalUser(TypeStatus[]? listStatus = null, DateTime? from = null, DateTime? to = null)
    {
        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Where(x => listStatus == null),
                Builders<User>.Filter.In(x => x.Status, listStatus ?? new TypeStatus[] { }) //Mongo required list not empty
            ),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Where(item => from == null),
                Builders<User>.Filter.Gte(x => x.Created, from)
            ),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Where(item => to == null),
                Builders<User>.Filter.Lt(x => x.Created, to)
            )
        );
        return _collectionUser.CountDocuments(filterBuilders);
    }

    public User? FindByUserId(string userId)
    {
        return _collectionUser.Find(item => item.UserId == userId).FirstOrDefault();
    }

    public User? FindByReferralCode(string referralCode, string shopId)
    {
        return _collectionUser
        .Find(item => item.ReferralCode == referralCode && item.ShopId == shopId)
        .FirstOrDefault();
    }

    public PagingResult<User> FindByUserIds(List<string> listIds, Paging paging)
    {
        PagingResult<User> result = new PagingResult<User>();
        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            Builders<User>.Filter.In(x => x.UserId, listIds ?? new List<string>()) //Mongo required list not empty
        );
        var query = _collectionUser.Find(filterBuilders);
        result.Total = query.CountDocuments();
        result.Result = query.Sort("{Created: -1}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public User? FindByUserEmail(string shopId, string email, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        return _collectionUser.Find(item => item.Email == email && item.ShopId == shopId &&
            (provider == null || item.Provider == provider) &&
            (provider == null || item.Status == status)).FirstOrDefault();
    }

    public User? FindByUserPhone(string shopId, string phonenumber, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        return _collectionUser.Find(item => item.ShopId == shopId && item.PhoneNumber == phonenumber &&
            (provider == null || item.Provider == provider) &&
            (status == null || item.Status == status)).FirstOrDefault();
    }

    public User? FindByUsername(string shopId, string username, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        return _collectionUser.Find(item => item.Username == username && item.ShopId == shopId &&
            (provider == null || item.Provider == provider) &&
            (status == null || item.Status == status)).FirstOrDefault();
    }

    //=== Check Email & Phone
    public bool CheckEmail(string shopId, string email, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        User user = _collectionUser.Find(item => item.Email == email && item.ShopId == shopId &&
            (provider == null || item.Provider == provider) &&
            (status == null || item.Status == status)).FirstOrDefault();
        if (user != null) return true;
        return false;
    }

    public bool CheckPhoneNumber(string shopId, string phonenumber, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        User user = _collectionUser.Find(item => item.PhoneNumber == phonenumber && item.ShopId == shopId &&
            (provider == null || item.Provider == provider) &&
            (status == null || item.Status == status)).FirstOrDefault();
        if (user != null) return true;
        return false;
    }

    public bool CheckUserName(string shopId, string username, TypeProviderLogin? provider = null, TypeStatus? status = null)
    {
        User user = _collectionUser.Find(item => item.Username == username && item.ShopId == shopId &&
            (provider == null || item.Provider == provider) &&
            (status == null || item.Status == status)).FirstOrDefault();
        if (user != null) return true;
        return false;
    }

    public bool IsEmailExistOnShop(string email, string shopId, string? currentUserId = null)
    {
        try
        {
            User userByEmail = FindByUserEmail(shopId, email);

            if (userByEmail != null && (currentUserId == null || userByEmail.UserId != currentUserId) && userByEmail.ShopId == shopId)
                return true;
        }
        catch (Exception ex)
        {
            _log4net.Error($"IsEmailExistOnShop failed: {ex.Message}", ex);
        }

        return false;
    }

    public bool IsPhoneExistOnShop(string phoneNumber, string shopId, string? currentUserId = null)
    {
        try
        {
            User userByPhone = FindByUserPhone(shopId, phoneNumber);

            if (userByPhone != null && (currentUserId == null || userByPhone.UserId != currentUserId) && userByPhone.ShopId == shopId)
                return true;
        }
        catch (Exception ex)
        {
            _log4net.Error($"IsPhoneExistOnShop failed: {ex.Message}", ex);
        }

        return false;
    }

    public PagingResult<User> GetUsers(Paging paging, string partnerId, string shopId, string tagName, RequiredUserDto model)
    {
        string searchQuery = ConvertPhoneNumber.NormalizePhoneNumber(paging.Search);
        PagingResult<User> result = new PagingResult<User>();

        // Danh sách filter chính
        var filters = new List<FilterDefinition<User>>
        {
            Builders<User>.Filter.Eq(x => x.ShopId, shopId),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Email, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.PhoneNumber, new BsonRegularExpression($@"{searchQuery}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.ReferralCode, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        };

        if (model.AffiliationStatus.HasValue)
            filters.Add(Builders<User>.Filter.Eq(x => x.AffiliationStatus, model.AffiliationStatus.Value));

        if (model.FromDate.HasValue)
            filters.Add(Builders<User>.Filter.Gte(x => x.Created, model.FromDate.Value));

        if (model.ToDate.HasValue)
            filters.Add(Builders<User>.Filter.Lte(x => x.Created, model.ToDate.Value));

        var filterBuilders = Builders<User>.Filter.And(filters);

        var query = _collectionUser.Find(filterBuilders);
        result.Total = query.CountDocuments();
        var users = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
                     .Skip(paging.PageIndex * paging.PageSize)
                     .Limit(paging.PageSize)
                     .ToList();

        result.Result = users;
        return result;
    }

    public List<string> ListUserIdsByShopId(string shopId)
    {
        return _collectionUser.Find(item => item.ShopId == shopId).ToList().Select(x => x.UserId).ToList();
    }

    public List<User> FindByUserIds(string userIds)
    {
        var listIds = userIds.Split(',');
        var filter = Builders<User>.Filter.In(x => x.UserId, listIds);
        return _collectionUser.Find(filter).ToList();
    }
    public async Task<User> FindUserByIdAsync(string userId)
    {
        var filter = Builders<User>.Filter.Eq(u => u.UserId, userId);
        return await _collectionUser.Find(filter).FirstOrDefaultAsync();
    }

    // Lấy danh sách F0 users với phân trang
    public PagingResult<User> GetF0Users(Paging paging, string shopId)
    {
        // Bước 1: Lọc F0 (Status Actived)
        var f0Filter = Builders<User>.Filter.Eq(u => u.Status, TypeStatus.Actived)
             & Builders<User>.Filter.Eq(u => u.ShopId, shopId)
             & Builders<User>.Filter.In("AffiliationStatus", new[] { AffiliationTypeStatus.Actived, AffiliationTypeStatus.Expired })
             & Builders<User>.Filter.Ne(u => u.AffiliationStatus, null);
        // Thêm điều kiện tìm kiếm Fullname chứa searchValue (nếu searchValue không rỗng)
        if (!string.IsNullOrWhiteSpace(paging.Search))
        {
            // Tìm kiếm không phân biệt hoa thường
            var searchFilter = Builders<User>.Filter.Regex(u => u.Fullname, new BsonRegularExpression(paging.Search, "i"))
                | Builders<User>.Filter.Regex(u => u.ReferralCode, new BsonRegularExpression(paging.Search, "i"));
            f0Filter &= searchFilter;
        }

        var f0Users = _collectionUser.Find(f0Filter);
        PagingResult<User> f0UsersPaging = new PagingResult<User>();

        // Paging
        f0UsersPaging.Total = f0Users.ToList().Count;
        f0UsersPaging.Result = f0Users.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
                                     .Skip(paging.PageIndex * paging.PageSize)
                                     .Limit(paging.PageSize)
                                     .ToList();
        return f0UsersPaging;
    }

    public List<User> ListUserByMembershipLevelId(string levelId, string shopId)
    {
        return _collectionUser.Find(item => item.MembershipLevelId == levelId && item.ShopId == shopId).ToList();
    }

    public IEnumerable<User> ListAllUsers(string shopId)
    {
        // Lọc người dùng theo shopId
        var filter = Builders<User>.Filter.Eq(user => user.ShopId, shopId);
        return _collectionUser.Find(filter).ToList(); // Lấy tất cả người dùng theo shopId
    }

    // Đếm số lượng F1 users và lấy danh sách F1 user IDs
    public async Task<(int f1Count, List<string> f1UserIds)> GetF1Users(string f0UserId)
    {
        var f1Filter = Builders<User>.Filter.Eq(f1 => f1.ParentId, f0UserId) &
                       Builders<User>.Filter.Eq(u => u.Status, TypeStatus.Actived);
        var f1Users = await _collectionUser.Find(f1Filter).ToListAsync();
        var f1Count = f1Users.Count;
        var f1UserIds = f1Users.Select(u => u.UserId).ToList();
        return (f1Count, f1UserIds);
    }

    // Đếm số lượng F2 users và lấy danh sách F2 user IDs
    public async Task<(int f2Count, List<string> f2UserIds)> GetF2Users(List<string> f1UserIds)
    {
        var f2Count = 0;
        var f2UserIds = new List<string>();

        if (f1UserIds.Any())
        {
            var f2Filter = Builders<User>.Filter.In(f2 => f2.ParentId, f1UserIds) &
                           Builders<User>.Filter.Eq(u => u.Status, TypeStatus.Actived);
            var f2Users = await _collectionUser.Find(f2Filter).ToListAsync();
            f2Count = f2Users.Count;
            f2UserIds.AddRange(f2Users.Select(u => u.UserId));
        }
        return (f2Count, f2UserIds);
    }

    public async Task<PagingResult<User>> ListUserFilteredByDate(PartnerInputDto filter, string? shopId = null)
    {
        PagingResult<User> result = new PagingResult<User>();

        // Tạo filter cho AffiliationStatus
        FilterDefinition<User> affiliationFilter;
        if (filter.AffiliationStatus == AffiliationTypeStatus.InActived)
        {
            affiliationFilter = Builders<User>.Filter.In("AffiliationStatus", new[] { AffiliationTypeStatus.InActived, AffiliationTypeStatus.Expired });
        }
        else
        {
            affiliationFilter = Builders<User>.Filter.Where(x => x.AffiliationStatus == filter.AffiliationStatus);
        }

        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            affiliationFilter,
            Builders<User>.Filter.Where(x => shopId == null || x.ShopId == shopId),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.ReferralCode, new BsonRegularExpression($@"{filter.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{filter.Search}".EscapeSpecialChars(), "i"))
            )
        );

        if (filter.FromDate.HasValue && filter.ToDate.HasValue)
        {
            var dateFilters = new List<FilterDefinition<User>>
        {
            Builders<User>.Filter.Gte(x => x.Created, filter.FromDate.Value.Date),
            Builders<User>.Filter.Lte(x => x.Created, filter.ToDate.Value.Date.AddDays(1).AddMilliseconds(-1))
        };

            if (dateFilters.Any())
                filterBuilders &= Builders<User>.Filter.And(dateFilters);
        }

        var query = _collectionUser.Find(filterBuilders);
        result.Result = await query.Sort($"{{{TypeSortName.Created}: {(-1)}}}")
                                    .Skip(Math.Max((filter.PageIndex - 1) * filter.PageSize, 0))
                                    .Limit(filter.PageSize)
                                    .ToListAsync();


        result.Total = await _collectionUser.CountDocumentsAsync(filterBuilders);

        return result;
    }

    public async Task<List<User>> FindReferers(string shopId, string? userId = null)
    {
        var filterDefinition = Builders<User>.Filter.Eq(x => x.Status, TypeStatus.Actived) &
                       Builders<User>.Filter.Eq(x => x.ShopId, shopId);

        if (!string.IsNullOrEmpty(userId))
        {
            filterDefinition &= Builders<User>.Filter.Nin(x => x.UserId, new[] { userId });
        }

        return await _collectionUser.Find(filterDefinition).ToListAsync();
    }
    public async Task<PagingResult<User>> ListExcelUser(PartnerInputDto filter, string? shopId = null)
    {
        PagingResult<User> result = new PagingResult<User>();
        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            Builders<User>.Filter.Where(x => x.AffiliationStatus == filter.AffiliationStatus),
            Builders<User>.Filter.Where(x => shopId == null || x.ShopId == shopId),
            Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.UserId, new BsonRegularExpression($@"{filter.Search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{filter.Search}".EscapeSpecialChars(), "i"))
            )
        );

        if (filter.FromDate.HasValue && filter.ToDate.HasValue)
        {
            var dateFilters = new List<FilterDefinition<User>>
        {
            Builders<User>.Filter.Gte(x => x.Created, filter.FromDate.Value.Date),
            Builders<User>.Filter.Lte(x => x.Created, filter.ToDate.Value.Date.AddDays(1).AddMilliseconds(-1))
        };

            if (dateFilters.Any())
                filterBuilders &= Builders<User>.Filter.And(dateFilters);
        }

        var query = _collectionUser.Find(filterBuilders).Sort($"{{{TypeSortName.Created}: {(-1)}}}");
        result.Result = await query.ToListAsync();
        return result;
    }

    public async Task<List<User>> ListUserByUserIds(List<string> userIds)
    {
        var users = await _collectionUser.Find(Builders<User>.Filter.In(u => u.UserId, userIds)).ToListAsync();
        return users;
    }

    public async Task<List<User>> GetUsersByFilter(FilterDefinition<User> filter)
    {
        return await _collectionUser.Find(filter).ToListAsync();
    }

    public async Task<byte[]> ExportUserTemplate()
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");
        var columns = ExportConst.HEADER_TEMPLATE_USER;

        // Thêm header
        for (int i = 0; i < columns.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = columns[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
        }

        // Data demo
        worksheet.Cells[2, 1].Value = "Nguyễn Văn A";
        worksheet.Cells[2, 2].Value = "<EMAIL>";
        worksheet.Cells[2, 3].Value = "0123456789";
        worksheet.Cells[2, 4].Value = "Nam";
        worksheet.Cells[2, 5].Value = "1990-01-01";

        // Áp dụng style header
        using (var range = worksheet.Cells[1, 1, 1, columns.Length])
        {
            range.Style.Font.Bold = true;
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;
            range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Áp dụng border + font cho toàn bộ bảng
        using (var range = worksheet.Cells[1, 1, 2, columns.Length])
        {
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;

            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Căn giữa các cột
        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Cells[1, i, 2, i].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        }

        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return package.GetAsByteArray();
    }

    public async Task<List<User>> GetListUsersWithBirthdayToday()
    {
        try
        {
            var today = DateTime.Now;
            var currentDay = today.Day;
            var currentMonth = today.Month;

            var filter = Builders<User>.Filter.And(
                Builders<User>.Filter.Eq(x => x.Status, TypeStatus.Actived),
                Builders<User>.Filter.Ne(x => x.Birthdate, null),
                Builders<User>.Filter.Where(x =>
                    x.Birthdate.HasValue &&
                    x.Birthdate.Value.Day == currentDay &&
                    x.Birthdate.Value.Month == currentMonth
                )
            );

            return await _collectionUser.Find(filter).ToListAsync();
        }
        catch (Exception ex)
        {
            _log4net.Error($"GetListUsersWithBirthdayToday failed: {ex.Message}");
            return new List<User>();
        }
    }

    public async Task<byte[]> ExportListUser(List<UserDto> list)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");

        // Định nghĩa các header
        var headers = ExportConst.HEADER_EXPPORT_USER;

        // Tạo header
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
        }

        // Áp dụng style cho header
        var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Font.Name = "Times New Roman";
        headerRange.Style.Font.Size = 13;
        headerRange.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        headerRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Nếu không có dữ liệu, trả về file với chỉ header
        if (list == null || list.Count == 0)
        {
            for (int i = 1; i <= headers.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            return package.GetAsByteArray();
        }

        // Thêm dữ liệu thủ công
        for (int i = 0; i < list.Count; i++)
        {
            int row = i + 2; // Bắt đầu từ dòng 2 (sau header)

            worksheet.Cells[row, 1].Value = i + 1; // STT
            worksheet.Cells[row, 2].Value = list[i].ReferralCode ?? ""; // Mã khách hàng
            worksheet.Cells[row, 3].Value = list[i].Fullname ?? ""; // Tên khách hàng
            worksheet.Cells[row, 4].Value = list[i].PhoneNumber ?? ""; // Số điện thoại
            worksheet.Cells[row, 5].Value = list[i].Email ?? ""; // Email
            worksheet.Cells[row, 6].Value = list[i].Point.ToString() ?? "0"; // Điểm thành viên
            worksheet.Cells[row, 7].Value = list[i].Address ?? ""; // Địa chỉ
            worksheet.Cells[row, 8].Value = list[i].Status.ToString(); // Trạng thái
            worksheet.Cells[row, 9].Value = list[i].Tags != null ? string.Join(", ", list[i].Tags) : ""; // Phân loại
            worksheet.Cells[row, 10].Value = list[i].MembershipLevel?.LevelName ?? ""; // Cấp độ thành viên
            worksheet.Cells[row, 11].Value = list[i].TotalSpent.ToString() ?? "0"; // Đã chi tiêu
            worksheet.Cells[row, 12].Value = list[i].Created.ToString("HH:mm:ss dd/MM/yyyy") ?? ""; // Thời gian kích hoạt

            try
            {
                var address = _shippingAddressRepository.FindDefaultShippingAddress(list[i].UserId);

                if (address != null && !string.IsNullOrEmpty(address.Address))
                    worksheet.Cells[row, 7].Value = $"{address.Address}, {address.WardName}, {address.DistrictName}, {address.ProvinceName}";
            }
            catch (Exception ex)
            {
                _log4net.Error($"ExportListUser Exception FindDefaultShippingAddress {list[i].UserId}: {ex.Message}");
            }
        }

        // Áp dụng style cho toàn bộ bảng
        var dataRange = worksheet.Cells[1, 1, list.Count + 1, headers.Length];
        dataRange.Style.Font.Name = "Times New Roman";
        dataRange.Style.Font.Size = 13;
        dataRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Căn giữa các cột
        worksheet.Cells[2, 1, list.Count + 1, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 2, list.Count + 1, 2].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 3, list.Count + 1, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
        worksheet.Cells[2, 4, list.Count + 1, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 5, list.Count + 1, 5].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
        worksheet.Cells[2, 6, list.Count + 1, 6].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
        worksheet.Cells[2, 7, list.Count + 1, 7].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
        worksheet.Cells[2, 8, list.Count + 1, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 9, list.Count + 1, 9].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 10, list.Count + 1, 10].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
        worksheet.Cells[2, 11, list.Count + 1, 11].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
        worksheet.Cells[2, 12, list.Count + 1, 12].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;

        for (int i = 1; i <= headers.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return package.GetAsByteArray();
    }

    public User? FindByZaloId(string zaloId)
    {
        return _collectionUser.Find(item => item.ZaloId == zaloId).FirstOrDefault();
    }

    public List<User> GetListUserExpireAffiliate(string shopId, double expirationYears)
    {
        // Tính ngày hết hạn dựa trên thời điểm hiện tại
        DateTime expirationThresholdDate = DateTime.Now.AddDays(-expirationYears * 365);

        // Truy vấn MongoDB: approvalDate < ngày hết hạn
        var expiredUsers = _collectionUser.Find(u =>
                u.ShopId == shopId &&
                u.ApprovalDate != null &&
                u.AffiliationStatus == AffiliationTypeStatus.Actived &&
                u.ApprovalDate < expirationThresholdDate
            )
            .ToList();

        return expiredUsers;
    }

    public async Task<List<User>> CreateUsers(List<User> users)
    {
        if (users == null || !users.Any())
            return new List<User>();

        await _collectionUser.InsertManyAsync(users);
        return users;
    }

    public async Task<User> UpdateUserPoint(string userId, int point)
    {
        var update = Builders<User>.Update
            .Set("Point", point)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<User>.Filter.Eq("Id", userId);
        var options = new FindOneAndUpdateOptions<User> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        var updatedUser = await _collectionUser.FindOneAndUpdateAsync(filter, update, options);
        return updatedUser;
    }
}