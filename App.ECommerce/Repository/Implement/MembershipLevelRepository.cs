﻿using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using App.ECommerce.Resource.Dtos.InputDtos;
using log4net;

namespace App.ECommerce.Repository.Implement;

public class MembershipLevelRepository : BaseRepository, IMembershipLevelRepository
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(MembershipLevelRepository));
    private readonly IMongoCollection<MembershipLevel> _collectionMembership;
    private readonly IMongoCollection<User> _collectionUser;
    private readonly IMongoCollection<PointTransaction> _collectionPointTransaction;
    private readonly IMongoCollection<ConfigMembershipLevel> _collectionConfigMembership;
    private readonly IMongoCollection<Order> _collectionOrder;

    public MembershipLevelRepository() : base()
    {
        _collectionMembership = _database.GetCollection<MembershipLevel>($"MembershipLevel");
        _collectionUser = _database.GetCollection<User>($"User");
        _collectionPointTransaction = _database.GetCollection<PointTransaction>($"PointTransaction");
        _collectionConfigMembership = _database.GetCollection<ConfigMembershipLevel>($"ConfigMembershipLevel");
        _collectionOrder = _database.GetCollection<Order>($"Order");
        var indexOptions = new CreateIndexOptions();
        var indexModel = new List<CreateIndexModel<MembershipLevel>>
        {
            new CreateIndexModel<MembershipLevel>(Builders<MembershipLevel>.IndexKeys.Ascending(item => item.LevelId), indexOptions),
            new CreateIndexModel<MembershipLevel>(Builders<MembershipLevel>.IndexKeys.Ascending(item => item.LevelName), indexOptions),
            new CreateIndexModel<MembershipLevel>(Builders<MembershipLevel>.IndexKeys.Ascending(item => item.ShopId), indexOptions)
        };
        _collectionMembership.Indexes.CreateMany(indexModel);
    }

    public PagingResult<MembershipLevel> ListMembershipShop(Paging paging, string shopId, string? partnerId = null, TypeActive? active = null)
    {
        PagingResult<MembershipLevel> result = new PagingResult<MembershipLevel>();
        FilterDefinition<MembershipLevel> filterBuilders = Builders<MembershipLevel>.Filter.And(
            Builders<MembershipLevel>.Filter.Where(p => p.ShopId == shopId),
            Builders<MembershipLevel>.Filter.Or(
                Builders<MembershipLevel>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<MembershipLevel>.Filter.Regex(x => x.LevelName, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );

        // Truy vấn tổng số lượng bản ghi
        result.Total = _collectionMembership.CountDocuments(filterBuilders);

        // Lấy danh sách với các trường cụ thể
        result.Result = _collectionMembership.Find(filterBuilders)
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList(); // Chuyển thành danh sách

        return result;
    }

    public MembershipLevel CreateMembershipLevel(MembershipLevel item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.LevelId = Guid.NewGuid().ToString();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionMembership.InsertOne(item);

        var isShopConfig = this.FindByConfigMembershipLevel(item.ShopId);
        if (isShopConfig == null)
        {
            ConfigMembershipLevel level = new ConfigMembershipLevel();
            ObjectId objectIdConfig = ObjectId.GenerateNewId();
            level.Id = new BsonObjectId(objectIdConfig).ToString();
            level.ConfigMembershipId = Guid.NewGuid().ToString();
            level.ShopId = item.ShopId;
            level.Status = true;
            level.EarnPoint = new EarnPointDto
            {
                OrderJson = new OrderJson
                {
                    Status = true,
                    IsPointAdd = TypeRewardCondition.delivery_success,
                    IsScore = TypePointCalculation.total_excluding_shipping
                },
                RegisterJson = new RegisterJson
                {
                    Rule = 0,
                    Status = true
                },
                ShareJson = new ShareJson
                {
                    Rule = 0,
                    MaxTurns = 0,
                    Status = true,
                    IsPurchase = new PurchaseJson
                    {
                        isRequire = false,
                        minSpent = 0,
                    }
                }
            }.ToJson();
            level.ExchangePoints = new ExchangePointsDto
            {
                DiscountJson = new DiscountJson
                {
                    Rule = 0,
                    MaxPercent = 100,
                    IsScore = new IsScore
                    {
                        IsValue = true,
                        Value = 0,
                        OptionDate = OptionDate.Month,
                    },
                    Status = true
                },
                VoucherJson = new VoucherJson
                {
                    Status = true,
                }
            }.ToJson();
            level.ValidUntil = TypeValidUntil.Unlimited;
            level.Created = DateTimes.Now();
            _collectionConfigMembership.InsertOne(level);
        }
        return item;
    }

    public MembershipLevel? FindByMembershipId(string levelId)
    {
        return _collectionMembership.Find(item => item.LevelId == levelId).FirstOrDefault();
    }

    public ConfigMembershipLevel? FindByConfigMembershipLevel(string shopId)
    {
        return _collectionConfigMembership.Find(item => item.ShopId == shopId).FirstOrDefault();
    }

    public MembershipLevel? UpdateMembership(MembershipLevel item)
    {
        MembershipLevel _item = _collectionMembership.Find(x => x.LevelId == item.LevelId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<MembershipLevel>.Update
    .Set("LevelName", item.LevelName)
    .Set("PointRate", item.PointRate)
    .Set("SpendingThreshold", item.SpendingThreshold)
    .Set("Image", item.Image)
    .Set("Updated", DateTimes.Now());
        var filter = Builders<MembershipLevel>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<MembershipLevel> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionMembership.FindOneAndUpdate(filter, update, options);
    }

    public MembershipLevel DeleteMembership(string levelId)
    {
        return _collectionMembership.FindOneAndDelete(item => item.LevelId == levelId);
    }

    public object GetSumDataUser(string shopId)
    {
        try
        {
            var result = _collectionUser.Aggregate()
                .Match(user => user.ShopId == shopId)
                .Group(new BsonDocument
                {
                    { "_id", BsonNull.Value },
                    { "totalPoints", new BsonDocument("$sum", new BsonDocument("$ifNull", new BsonArray { "$Point", 0 })) },
                    { "userCount", new BsonDocument("$sum", new BsonDocument("$cond", new BsonArray
                        {
                            new BsonDocument("$gt", new BsonArray { new BsonDocument("$ifNull", new BsonArray { "$Point", 0 }), 0 }),
                            1,
                            0
                        })) },
                    { "totalUser", new BsonDocument("$sum", 1) }
                })
                .FirstOrDefault();

            // Tối ưu: Xử lý null an toàn hơn
            var totalPoints = result?["totalPoints"]?.ToInt64() ?? 0;
            var userCount = result?["userCount"]?.ToInt32() ?? 0;
            var totalUser = result?["totalUser"]?.ToInt32() ?? 0;

            return new
            {
                TotalPoints = totalPoints,
                UserCount = userCount,
                TotalUser = totalUser
            };
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetSumDataUser for shopId: {shopId}", ex);

            return new
            {
                TotalPoints = 0L,
                UserCount = 0,
                TotalUser = 0
            };
        }
    }

    public TransactionPointDto CalculateUserScoreDetails(User user)
    {
        var sumPoints = _collectionPointTransaction
            .Find(t => t.UserId == user.UserId && t.IsAdditionEnabled == true)
            .ToList()
            .Sum(t => Math.Abs(t.PointsEarned));

        var spentPoints = _collectionPointTransaction
            .Find(t => t.UserId == user.UserId && t.IsAdditionEnabled == false)
            .ToList()
            .Sum(t => Math.Abs(t.PointsEarned));

        var totalSpentAmount = _collectionOrder.Aggregate()
            .Match(o => o.ShopId == user.ShopId &&
                       o.Creator.UserId == user.UserId &&
                       o.StatusOrder == TypeOrderStatus.Success)
            .Group(new BsonDocument
            {
                { "_id", BsonNull.Value },
                { "totalAmount", new BsonDocument("$sum", "$Price") }
            })
            .FirstOrDefault()?["totalAmount"].ToDouble() ?? 0;

        FilterDefinition<MembershipLevel> filterMembershipBuilders = Builders<MembershipLevel>.Filter.And(
            Builders<MembershipLevel>.Filter.Where(p => p.LevelId == user.MembershipLevelId)
        );

        var mbsUser = _collectionMembership.Find(filterMembershipBuilders).FirstOrDefault();

        return new TransactionPointDto
        {
            UserId = user.UserId,
            ShopId = user.ShopId,
            FullName = user.Fullname,
            Avatar = user.Avatar,
            Phone = user.PhoneNumber,
            CurrentPoint = (sumPoints - Math.Abs(spentPoints)), // Điểm hiện có
            TotalPoint = (long)sumPoints, // Tổng điểm tích lũy
            SpentPoint = Math.Abs(spentPoints), // Điểm đã tiêu
            SpentAmount = (long)totalSpentAmount,
            MembershipLevel = mbsUser,
        };
    }

    public PagingResult<TransactionPointDto> ListDetailUserScore(Paging paging, string shopId, string? search, string? partnerId = null, TypeActive? active = null)
    {
        PagingResult<TransactionPointDto> result = new PagingResult<TransactionPointDto>();
        string searchQuery = ConvertPhoneNumber.NormalizePhoneNumber(search);

        // Build search filter
        var searchFilter = string.IsNullOrEmpty(search)
            ? Builders<User>.Filter.Empty
            : Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.Username, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Email, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.PhoneNumber, new BsonRegularExpression($@"{Regex.Escape(searchQuery)}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i"))
            );

        // Combine with shop filter
        FilterDefinition<User> filterBuilders = Builders<User>.Filter.And(
            Builders<User>.Filter.Where(p => p.ShopId == shopId),
            searchFilter
        );

        result.Total = _collectionUser.CountDocuments(filterBuilders);

        var users = _collectionUser.Find(filterBuilders)
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList();

        result.Result = users.Select(user => CalculateUserScoreDetails(user)).ToList();

        return result;
    }

    public TransactionPointDto DetailUserScore(string shopId, string userId)
    {
        var user = _collectionUser.Find(x => x.UserId == userId && x.ShopId == shopId).FirstOrDefault();
        if (user == null) return null;

        return CalculateUserScoreDetails(user);
    }

    public PagingResult<PointExchangeHistoryDto> PointExchangeHistory(Paging paging, string shopId, TypeTransaction type, DateTime toDate, DateTime fromDate, string? search)
    {
        PagingResult<PointExchangeHistoryDto> result = new PagingResult<PointExchangeHistoryDto>();

        var fromDateTime = fromDate.Kind == DateTimeKind.Unspecified ? DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) : fromDate;
        var toDateTime = toDate.Kind == DateTimeKind.Unspecified ? DateTime.SpecifyKind(toDate, DateTimeKind.Utc) : toDate;

        var filter = Builders<PointTransaction>.Filter.And(
            Builders<PointTransaction>.Filter.Eq(x => x.ShopId, shopId),
            Builders<PointTransaction>.Filter.Gte(x => x.Created, fromDateTime),
            Builders<PointTransaction>.Filter.Lte(x => x.Created, toDateTime)
        );

        if (type != TypeTransaction.ALL)
        {
            filter = Builders<PointTransaction>.Filter.And(
                filter,
                Builders<PointTransaction>.Filter.Eq(x => x.Type, type)
            );
        }

        // Get user IDs if search by username is provided
        var userIds = new List<string>();
        if (!string.IsNullOrEmpty(search))
        {
            string searchQuery = ConvertPhoneNumber.NormalizePhoneNumber(search);


            // Tạo bộ lọc cho shopId
            var shopIdFilter = Builders<User>.Filter.Eq(x => x.ShopId, shopId);

            // Tạo bộ lọc cho các điều kiện tìm kiếm khác
            var nameFilter = Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i"));
            var phoneFilter = Builders<User>.Filter.Regex(x => x.PhoneNumber, new BsonRegularExpression($@"{searchQuery}".EscapeSpecialChars(), "i"));

            // Kết hợp bộ lọc shopId với các điều kiện tìm kiếm khác
            var userNameFilter = Builders<User>.Filter.And(shopIdFilter, Builders<User>.Filter.Or(nameFilter, phoneFilter));

            userIds = _collectionUser.Find(userNameFilter)
                .Project(x => x.UserId)
                .ToList();


            // Add user filter if we found matching users
            // if (userIds.Any())
            // {
            filter = Builders<PointTransaction>.Filter.And(
                filter,
                Builders<PointTransaction>.Filter.In(x => x.UserId, userIds)
            );
            // }
        }

        // Get total count
        result.Total = _collectionPointTransaction.CountDocuments(filter);
        // Get transactions with pagination
        var transactions = _collectionPointTransaction.Find(filter)
            .Sort(Builders<PointTransaction>.Sort.Descending(x => x.Created))
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList();

        // Get user information for all transactions
        var transactionUserIds = transactions.Select(t => t.UserId).Distinct().ToList();
        var users = _collectionUser.Find(Builders<User>.Filter.In(x => x.UserId, transactionUserIds))
            .Project(x => new { x.UserId, x.Username, x.Fullname, x.Avatar })
            .ToList()
            .ToDictionary(x => x.UserId, x => new { x.Username, x.Fullname, x.Avatar });

        // Map transactions to DTOs
        result.Result = transactions.Select(t => new PointExchangeHistoryDto
        {
            TransactionId = t.Id,
            UserId = t.UserId,
            UserName = users.ContainsKey(t.UserId) ? users[t.UserId].Username : "Unknown User",
            FullName = users.ContainsKey(t.UserId) ? users[t.UserId].Fullname : "Unknown User",
            Type = t.Type,
            Created = t.Created,
            PointsEarned = t.PointsEarned,
            IsAdditionEnabled = t.IsAdditionEnabled,
            Note = t.Note,
            Detail = t.Detail,
            Avatar = users.ContainsKey(t.UserId) ? users[t.UserId].Avatar : "Unknown User",
        }).ToList();

        return result;
    }

    public PagingResult<PointExchangeHistoryDto> GetIncomeHistory(Paging paging, string shopId, string userId)
    {
        PagingResult<PointExchangeHistoryDto> result = new PagingResult<PointExchangeHistoryDto>();

        // Build filter for Plus transactions and shopId
        var filter = Builders<PointTransaction>.Filter.And(
            Builders<PointTransaction>.Filter.Eq(x => x.IsAdditionEnabled, true),
            Builders<PointTransaction>.Filter.Eq(x => x.ShopId, shopId),
            Builders<PointTransaction>.Filter.Eq(x => x.UserId, userId)
        );

        // Get total count
        result.Total = _collectionPointTransaction.CountDocuments(filter);

        // Get transactions with pagination and specific fields
        var transactions = _collectionPointTransaction.Find(filter)
            .Project(x => new
            {
                x.Id,
                x.Created,
                x.PointsEarned,
                x.Type,
                x.Detail,
                x.UserId,
                x.IsAdditionEnabled,
                x.ShopId
            })
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList();

        // Get user information for all transactions
        var transactionUserIds = transactions.Select(t => t.UserId).Distinct().ToList();
        var users = _collectionUser.Find(Builders<User>.Filter.In(x => x.UserId, transactionUserIds))
            .Project(x => new { x.UserId, x.Username })
            .ToList()
            .ToDictionary(x => x.UserId, x => x.Username);

        // Map transactions to DTOs

        result.Result = transactions.OrderByDescending(t => t.Created).Select(t => new PointExchangeHistoryDto
        {
            TransactionId = t.Id,
            UserId = t.UserId,
            UserName = users.ContainsKey(t.UserId) ? users[t.UserId] : "Unknown User",
            Type = t.Type,
            Created = t.Created,
            PointsEarned = t.PointsEarned,
            Note = t.Detail,
            IsAdditionEnabled = t.IsAdditionEnabled
        }).ToList();

        return result;
    }

    public PagingResult<PointExchangeHistoryDto> GetSpendingHistory(Paging paging, string shopId, string userId)
    {
        PagingResult<PointExchangeHistoryDto> result = new PagingResult<PointExchangeHistoryDto>();

        // Build filter for Minus transactions
        var filter = Builders<PointTransaction>.Filter.And(
            Builders<PointTransaction>.Filter.Eq(x => x.IsAdditionEnabled, false),
            Builders<PointTransaction>.Filter.Eq(x => x.ShopId, shopId),
            Builders<PointTransaction>.Filter.Eq(x => x.UserId, userId)
        );

        // Get total count
        result.Total = _collectionPointTransaction.CountDocuments(filter);

        // Get transactions with pagination and specific fields
        var transactions = _collectionPointTransaction.Find(filter)
            .Project(x => new
            {
                x.Id,
                x.Created,
                x.PointsEarned,
                x.Type,
                x.Detail,
                x.UserId,
                x.IsAdditionEnabled
            })
            .SortByDescending(x => x.Created) // Fix: Use SortByDescending instead of OrderByDescending
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList();

        // Get user information for all transactions
        var transactionUserIds = transactions.Select(t => t.UserId).Distinct().ToList();
        var users = _collectionUser.Find(Builders<User>.Filter.In(x => x.UserId, transactionUserIds))
            .Project(x => new { x.UserId, x.Username })
            .ToList()
            .ToDictionary(x => x.UserId, x => x.Username);

        // Map transactions to DTOs
        result.Result = transactions.OrderByDescending(t => t.Created).Select(t => new PointExchangeHistoryDto
        {
            TransactionId = t.Id,
            UserId = t.UserId,
            UserName = users.ContainsKey(t.UserId) ? users[t.UserId] : "Unknown User",
            Type = t.Type,
            Created = t.Created,
            PointsEarned = t.PointsEarned,
            Note = t.Detail,
            IsAdditionEnabled = t.IsAdditionEnabled
        }).ToList();

        return result;
    }

    public ConfigMembershipLevel? GetConfigMembership(string shopId)
    {
        return _collectionConfigMembership.Find(x => x.ShopId == shopId)
            .Project(x => new ConfigMembershipLevel
            {
                Id = x.Id,
                ShopId = x.ShopId,
                Status = x.Status,
                EarnPoint = x.EarnPoint,
                ExchangePoints = x.ExchangePoints,
                ValidUntil = x.ValidUntil
            })
            .FirstOrDefault();
    }

    public ConfigMembershipLevel EditConfigMembership(ConfigMembershipLevel model)
    {
        ConfigMembershipLevel _item = _collectionConfigMembership.Find(x => x.ShopId == model.ShopId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<ConfigMembershipLevel>.Update
            .Set("ShopId", model.ShopId)
            .Set("Status", model.Status)
            .Set("EarnPoint", model.EarnPoint)
            .Set("ExchangePoints", model.ExchangePoints)
            .Set("ValidUntil", model.ValidUntil)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<ConfigMembershipLevel>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<ConfigMembershipLevel> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        var updatedItem = _collectionConfigMembership.FindOneAndUpdate(filter, update, options);

        return new ConfigMembershipLevel
        {
            ShopId = updatedItem.ShopId,
            Status = updatedItem.Status,
            EarnPoint = updatedItem.EarnPoint,
            ExchangePoints = updatedItem.ExchangePoints,
            ValidUntil = updatedItem.ValidUntil
        };
    }

    public ConfigMembershipLevel? FindByConfigMembershipId(string shopId)
    {
        return _collectionConfigMembership.Find(item => item.ShopId == shopId).FirstOrDefault();
    }

    public MembershipLevel? FindMemberShipLevelBySpendingThreshold(long spendingThreshold, string shopId)
    {
        return _collectionMembership.Find(item => item.SpendingThreshold == spendingThreshold && item.ShopId == shopId).FirstOrDefault();
    }

    public MembershipLevel? NextMembershipLevel(long spendingThreshold, string shopId)
    {
        return _collectionMembership.Find(item => item.SpendingThreshold <= spendingThreshold && item.ShopId == shopId)
        .SortByDescending(item => item.SpendingThreshold) // Sắp xếp theo spendingThreshold giảm dần
         .FirstOrDefault(); // Lấy cấp độ đầu tiên tìm thấy
    }

    public PointTransaction? FindPointTransactionBy(string shopId, string orderId = null, TypeTransaction? typeTransaction = null, string userId = null, string referrerUserId = null)
    {
        var filterBuilder = Builders<PointTransaction>.Filter;
        var filters = new List<FilterDefinition<PointTransaction>>();

        // Luôn thêm điều kiện shopId
        filters.Add(filterBuilder.Eq(x => x.ShopId, shopId));

        // Thêm điều kiện vào bộ lọc nếu tham số không phải là null
        if (!string.IsNullOrEmpty(orderId))
        {
            filters.Add(filterBuilder.Eq(x => x.OrderId, orderId));
        }

        if (!string.IsNullOrEmpty(userId))
        {
            filters.Add(filterBuilder.Eq(x => x.UserId, userId));
        }

        if (typeTransaction.HasValue)
        {
            filters.Add(filterBuilder.Eq(x => x.Type, typeTransaction.Value));
        }

        if (!string.IsNullOrEmpty(referrerUserId))
        {
            filters.Add(filterBuilder.Eq(x => x.ReferrerUserId, referrerUserId));
        }

        // Kết hợp các bộ lọc với AND
        var finalFilter = filterBuilder.And(filters);

        return _collectionPointTransaction.Find(finalFilter).FirstOrDefault();
    }

    public int CountShareByUserId(string shopId, string userId)
    {
        var filterBuilder = Builders<PointTransaction>.Filter;
        var filters = new List<FilterDefinition<PointTransaction>>
        {
            // Luôn thêm điều kiện shopId
            filterBuilder.Eq(x => x.ShopId, shopId),
            filterBuilder.Eq(x => x.UserId, userId),
            filterBuilder.Eq(x => x.Type, TypeTransaction.Introduce)
        };
        // Kết hợp các bộ lọc với AND
        var finalFilter = filterBuilder.And(filters);

        // Đếm số lượng PointTransaction theo bộ lọc
        return (int)_collectionPointTransaction.CountDocuments(finalFilter);
    }

    public List<PointTransaction>? ListPointTransactionBy(PointTransactionFilterDto obj)
    {
        var filterBuilder = Builders<PointTransaction>.Filter;
        var filters = new List<FilterDefinition<PointTransaction>>();

        // Luôn thêm điều kiện shopId
        filters.Add(filterBuilder.Eq(x => x.ShopId, obj.ShopId));

        // Thêm điều kiện vào bộ lọc nếu tham số không phải là null
        if (obj.IsAdditionEnabled != null)
        {
            filters.Add(filterBuilder.Eq(x => x.IsAdditionEnabled, obj.IsAdditionEnabled));
        }

        // Thêm điều kiện vào bộ lọc nếu tham số không phải là null
        if (!string.IsNullOrEmpty(obj.OrderId))
        {
            filters.Add(filterBuilder.Eq(x => x.OrderId, obj.OrderId));
        }

        if (!string.IsNullOrEmpty(obj.UserId))
        {
            filters.Add(filterBuilder.Eq(x => x.UserId, obj.UserId));
        }

        if (obj.TypeTransaction.HasValue)
        {
            filters.Add(filterBuilder.Eq(x => x.Type, obj.TypeTransaction.Value));
        }

        if (!string.IsNullOrEmpty(obj.ReferrerUserId))
        {
            filters.Add(filterBuilder.Eq(x => x.ReferrerUserId, obj.ReferrerUserId));
        }

        // Thêm filter theo khoảng thời gian
        if (obj.FromDate.HasValue)
        {
            filters.Add(filterBuilder.Gte(x => x.Created, obj.FromDate.Value));
        }

        if (obj.ToDate.HasValue)
        {
            filters.Add(filterBuilder.Lte(x => x.Created, obj.ToDate.Value));
        }

        // Thêm điều kiện vào bộ lọc nếu tham số không phải là null
        if (obj.IsRefund != null)
        {
            filters.Add(filterBuilder.Eq(x => x.IsRefund, obj.IsRefund));
        }

        // Kết hợp các bộ lọc với AND
        var finalFilter = filterBuilder.And(filters);

        return _collectionPointTransaction.Find(finalFilter).ToList();
    }

    public async Task<PointTransaction> UpdatePointTransaction(PointTransaction item)
    {
        PointTransaction _item = _collectionPointTransaction.Find(x => x.PointTransactionId == item.PointTransactionId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<PointTransaction>.Update
            .Set("IsRefund", item.IsRefund)
            .Set("UserId", item.UserId)
            .Set("Type", item.Type)
            .Set("Detail", item.Detail)
            .Set("PointsEarned", item.PointsEarned)
            .Set("IsAdditionEnabled", item.IsAdditionEnabled)
            .Set("Note", item.Note)
            .Set("Spent", item.Spent)
            .Set("OrderId", item.OrderId)
            .Set("ReferrerUserId", item.ReferrerUserId)
            .Set("Expired", item.Expired)
            .Set("ExpiredAt", item.ExpiredAt)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<PointTransaction>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<PointTransaction> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return await _collectionPointTransaction.FindOneAndUpdateAsync(filter, update, options);
    }

    public ConfigMembershipLevel CreateConfigMembershipLevel(ConfigMembershipLevel item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.ConfigMembershipId = Guid.NewGuid().ToString();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionConfigMembership.InsertOne(item);
        return item;
    }
}
