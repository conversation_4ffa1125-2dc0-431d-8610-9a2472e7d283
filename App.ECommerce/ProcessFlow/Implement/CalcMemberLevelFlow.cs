using System;

using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Units;

using AutoMapper;

using log4net;

using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

namespace App.ECommerce.ProcessFlow.Implement;

public class CalcMemberLevelFlow : ICalcMemberLevelFlow
{
    private readonly ILog _log = LogManager.GetLogger(typeof(CalcMemberLevelFlow));
    private readonly IUserRepository _userRepository;
    private readonly ICartRepository _cartRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly IMapper _mapper;

    public CalcMemberLevelFlow(
        IUserRepository userRepository,
        ICartRepository cartRepository,
        IOrderRepository orderRepository,
        IMembershipLevelRepository membershipLevelRepository,
        IMapper mapper
    )
    {
        _userRepository = userRepository;
        _cartRepository = cartRepository;
        _orderRepository = orderRepository;
        _membershipLevelRepository = membershipLevelRepository;
        _mapper = mapper;
    }

    #region Public methods
    public async Task<(string? errors, User? user)> CalcUserPointByOrder(string orderId)
    {
        try
        {
            var (error, order) = ValidateOrder(orderId);
            if (error != null) return (error, null);

            _log.Info($"CalcUserPointByOrder Kiểm tra order.ShopId {order.ShopId} | order.OrderId: {order.OrderId}");

            var pointTransaction = _membershipLevelRepository.FindPointTransactionBy(shopId: order.ShopId, orderId: order.OrderId, typeTransaction: TypeTransaction.Shop);
            _log.Info($"CalcUserPointByOrder Kiểm tra điểm cho đơn hàng {orderId} - pointTransaction:  {JsonConvert.SerializeObject(pointTransaction)}");
            if (pointTransaction != null) return ("Đã tính điểm rồi", null);

            var (userError, user) = ValidateUserAndConfig(order.Creator.UserId, order.ShopId);
            _log.Info($"CalcUserPointByOrder Kiểm tra user {order.Creator.UserId} - Lỗi: {userError}");
            if (userError != null) return (userError, null);

            var (levelError, updatedUser) = ValidateAndUpdateFreeMembershipLevel(user);
            _log.Info($"CalcUserPointByOrder Kiểm tra membership level cho user {order.Creator.UserId} - Lỗi: {levelError}");
            if (levelError != null) return (levelError, null);

            var (membershipError, userMembershipLevel) = ValidateUserMembershipLevel(updatedUser.MembershipLevelId);
            _log.Info($"CalcUserPointByOrder Kiểm tra membership level cho user {order.Creator.UserId} - Lỗi: {membershipError}");
            if (membershipError != null) return (membershipError, null);

            var (configError, earnPointDto) = ValidateAndGetEarnPointConfig(order.ShopId, null);
            _log.Info($"CalcUserPointByOrder Kiểm tra config earn point cho shop {order.ShopId} - Lỗi: {configError}");
            if (configError != null) return (configError, null);
            if (!earnPointDto.OrderJson.Status) return ("Đã tắt tích điểm khi mua hàng", null);

            var (ratePointError, ratePoint) = GetRatePointFromConfig(order.ShopId);
            _log.Info($"CalcUserPointByOrder Kiểm tra rate point cho shop {order.ShopId} - Lỗi: {ratePointError}");
            if (ratePointError != null) return (ratePointError, null);

            var (_, newPoint) = CalculatePoints(
                order.Price,
                userMembershipLevel.PointRate,
                earnPointDto.OrderJson.IsScore,
                ratePoint,
                order.TransportPrice,
                order.VoucherTransportPrice
            );

            _log.Info($"CalcUserPointByOrder Tính điểm cho đơn hàng {order.StatusPay} - newPoint: {newPoint}");

            if ((earnPointDto.OrderJson.IsPointAdd == TypeRewardCondition.payment_success && order.StatusPay == TypePayStatus.Paid) ||
                (earnPointDto.OrderJson.IsPointAdd == TypeRewardCondition.delivery_success && order.StatusOrder == TypeOrderStatus.Success))
            {
                if (order.PointAfterCompleteOrder > 0)
                {
                    var objPointTransaction = new PointTransaction
                    {
                        ShopId = order.ShopId,
                        UserId = order.Creator.UserId,
                        PointsEarned = order.PointAfterCompleteOrder ?? newPoint,
                        IsAdditionEnabled = true,
                        Type = TypeTransaction.Shop,
                        Note = $"Cộng điểm đơn hàng #{order.OrderNo}",
                        Detail = $"Cộng điểm đơn hàng #{order.OrderNo}",
                        OrderId = order.OrderId
                    };

                    await UpdateUserPoint(objPointTransaction);
                }
            }

            return (null, updatedUser);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tính điểm cho đơn hàng {orderId}: {ex.Message}", ex);
            return ($"Đã xảy ra lỗi: {ex.Message}", null);
        }
    }

    public async Task<(string? errors, User? user)> CalcUserLevel(string userId)
    {
        try
        {
            var user = _userRepository.FindByUserId(userId);
            if (user == null)
            {
                return ("User not found", null); // Return an error message if user is not found
            }

            var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(user.ShopId);
            if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);

            if (configMembershipLevel != null)
            {
                // Phân tích cú pháp chuỗi JSON EarnPoint và ánh xạ vào đối tượng EarnPointDto
                var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);

                var isScore = earnPointDto.OrderJson.IsScore; // Kiểu TypePointCalculation

                long orderTotalPrice;

                orderTotalPrice = await _orderRepository.TotalPriceOrderByUserId(userId, isScore);

                var nextMembershipLevel = _membershipLevelRepository.NextMembershipLevel(orderTotalPrice, user.ShopId);
                if (nextMembershipLevel != null && user.MembershipLevelId != nextMembershipLevel.LevelId)
                {
                    user.MembershipLevelId = nextMembershipLevel.LevelId;
                    user = _userRepository.UpdateUser(user);
                }


                return (null, user); // Return the user if found, with no errors
            }
            return ("Không tìm thấy config membership", null); // Return an error message if user is not found
        }
        catch (Exception ex)
        {
            // Ghi log hoặc xử lý ngoại lệ theo cách bạn muốn
            return ($"Đã xảy ra lỗi: {ex.Message}", null);
        }

    }

    public async Task<(string? errors, User? user)> CalcUserPointBySignUp(string userId)
    {
        try
        {
            var user = _userRepository.FindByUserId(userId);
            if (user == null) return ("Không tìm thấy user", null);

            var pointRegisterTransaction = _membershipLevelRepository.FindPointTransactionBy(shopId: user.ShopId, typeTransaction: TypeTransaction.Register, userId: userId);
            if (pointRegisterTransaction != null) return ("User đã đổi điểm khi đăng ký", null);

            var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(user.ShopId);
            if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
            if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);

            // Phân tích cú pháp chuỗi JSON EarnPoint
            var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);
            if (!earnPointDto.RegisterJson.Status) return ("Đã tắt tích điểm khi đăng ký", null);

            var newPoint = earnPointDto.RegisterJson.Rule;
            if (newPoint > 0)
            {
                var objPointTransaction = new PointTransaction
                {
                    ShopId = user.ShopId,
                    UserId = user.UserId,
                    PointsEarned = newPoint,
                    IsAdditionEnabled = true,
                    Type = TypeTransaction.Register,
                    Note = $"Cộng điểm khách hàng đăng ký thành công",
                    Detail = $"Cộng điểm khách hàng đăng ký thành công"
                };

                await UpdateUserPoint(objPointTransaction);
            }

            return (null, user);
        }
        catch (Exception ex)
        {
            // Ghi log hoặc xử lý ngoại lệ theo cách bạn muốn
            return ($"Đã xảy ra lỗi: {ex.Message}", null);
        }

    }

    public async Task<(string? errors, User? user)> CalcUserPointByShare(string referrerUserId)
    {
        try
        {
            var referrerUser = _userRepository.FindByUserId(referrerUserId);
            if (referrerUser == null) return ("Không tìm thấy user đăng ký", null);
            if (string.IsNullOrEmpty(referrerUser.ReferrerCode)) return ("User không nhập mã giới thiệu", null);

            var user = _userRepository.FindByReferralCode(referrerUser.ReferrerCode, referrerUser.ShopId);
            if (user == null) return ("Không tìm thấy user giới thiệu", null);

            var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(user.ShopId);
            if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
            if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);
            // Phân tích cú pháp chuỗi JSON EarnPoint
            var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);
            if (!earnPointDto.ShareJson.Status) return ("Đã tắt tích điểm khi giới thiệu khách hàng ", null);

            //Validate có yêu cầu mua hàng không
            var isRequirePurchase = earnPointDto.ShareJson.IsPurchase.isRequire;
            if (isRequirePurchase) return ("Yêu cầu mua hàng mới được tích điểm", null);

            //Validate user đã được tích điểm chưa
            var sharePointTransaction = _membershipLevelRepository.FindPointTransactionBy(shopId: user.ShopId, typeTransaction: TypeTransaction.Introduce, userId: user.UserId, referrerUserId: referrerUserId);
            if (sharePointTransaction != null) return ("User đã đổi điểm", null);

            //Validate Số lượt chia sẻ tối đa được nhận thưởng
            var maxShareCount = earnPointDto.ShareJson.MaxTurns;
            var shareCount = _membershipLevelRepository.CountShareByUserId(user.ShopId, user.UserId);
            if (shareCount >= maxShareCount) return ("Vượt quá số lần đổi điểm", null);

            var newPoint = earnPointDto.ShareJson.Rule;
            if (newPoint > 0)
            {
                var objPointTransaction = new PointTransaction
                {
                    ShopId = user.ShopId,
                    UserId = user.UserId,
                    PointsEarned = newPoint,
                    IsAdditionEnabled = true,
                    Type = TypeTransaction.Introduce,
                    Note = $"Cộng điểm khách hàng chia sẻ cửa hàng",
                    Detail = $"Cộng điểm khách hàng chia sẻ cửa hàng",
                    OrderId = null,
                    ReferrerUserId = referrerUserId
                };

                await UpdateUserPoint(objPointTransaction);
            }

            return (null, user);
        }
        catch (Exception ex)
        {
            // Ghi log hoặc xử lý ngoại lệ theo cách bạn muốn
            return ($"Đã xảy ra lỗi: {ex.Message}", null);
        }

    }

    public async Task<(string? errors, User? user)> CalcUserPointsForReferralOrder(string orderId)
    {
        try
        {
            var order = _orderRepository.FindByOrderId(orderId);
            if (order == null) return ("Không tìm thấy order", null);

            if (order.Creator?.UserId == null) return ("Không tích điểm cho đơn khách vãng lai", null);
            if (order.StatusOrder != TypeOrderStatus.Success) return ("Đơn chưa hoàn thành", null);

            var userOrder = _userRepository.FindByUserId(order.Creator?.UserId);
            if (userOrder == null) return ("Không tìm thấy user của đơn hàng", null);

            if (string.IsNullOrEmpty(userOrder.ReferrerCode)) return ("Không tìm thấy mã người giới thiệu", null);

            var user = _userRepository.FindByReferralCode(userOrder.ReferrerCode, userOrder.ShopId);

            if (user == null) return ("Không tìm thấy người giới thiệu", null);

            var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(user.ShopId);
            if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
            if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);
            // Phân tích cú pháp chuỗi JSON EarnPoint
            var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);
            if (!earnPointDto.ShareJson.Status) return ("Đã tắt tích điểm khi giới thiệu khách hàng ", null);

            //Validate có yêu cầu mua hàng không
            var isRequirePurchase = earnPointDto.ShareJson.IsPurchase.isRequire;
            if (!isRequirePurchase) return ("Yêu cầu mua hàng mới được tích điểm", null);

            //Validate user đã được tích điểm chưa
            var sharePointTransaction = _membershipLevelRepository.FindPointTransactionBy(shopId: user.ShopId, typeTransaction: TypeTransaction.Introduce, userId: user.UserId, referrerUserId: order.Creator.UserId);
            if (sharePointTransaction != null) return ("User đã đổi điểm", null);

            //Validate Số lượt chia sẻ tối đa được nhận thưởng
            var maxShareCount = earnPointDto.ShareJson.MaxTurns;
            var shareCount = _membershipLevelRepository.CountShareByUserId(user.ShopId, user.UserId);
            if (shareCount >= maxShareCount) return ("Vượt quá số lần đổi điểm", null);

            var isScore = earnPointDto.OrderJson.IsScore; // Kiểu TypePointCalculation

            long orderTotalPrice = await _orderRepository.TotalPriceOrderByUserId(order.Creator.UserId, isScore);

            long requireMinSpent = earnPointDto.ShareJson.IsPurchase.minSpent;
            if (orderTotalPrice < requireMinSpent) return ("Tổng số tiền chi tiêu tối thiểu của người được giới thiệu chưa đạt mức tối thiểu", null);
            var newPoint = earnPointDto.ShareJson.Rule;
            if (newPoint > 0)
            {
                var objPointTransaction = new PointTransaction
                {
                    ShopId = user.ShopId,
                    UserId = user.UserId,
                    PointsEarned = newPoint,
                    IsAdditionEnabled = true,
                    Type = TypeTransaction.Introduce,
                    Note = $"Cộng điểm khách hàng được giới thiệu mua hàng đơn hàng #{order.OrderNo}",
                    Detail = $"Cộng điểm khách hàng được giới thiệu mua hàng đơn hàng #{order.OrderNo}",
                    OrderId = order.OrderId,
                    ReferrerUserId = order.Creator.UserId
                };

                await UpdateUserPoint(objPointTransaction);

                return (null, user);
            }

            return ($"Điểm tích = {newPoint}", null);
        }
        catch (Exception ex)
        {
            // Ghi log hoặc xử lý ngoại lệ theo cách bạn muốn
            return ($"Đã xảy ra lỗi: {ex.Message}", null);
        }

    }

    public async Task<(string? errors, int point, long pointPrice)> CalcMaxPointCanUse(long totalPrice, string userId)
    {
        try
        {
            long point = 0;

            var user = _userRepository.FindByUserId(userId);
            if (user == null) return ("Không tìm thấy user", 0, 0);

            var transaction = _membershipLevelRepository.CalculateUserScoreDetails(user);

            if (transaction == null) return ("Không tìm thấy user", 0, 0);
            if (transaction.CurrentPoint <= 0) return ("User không có điểm", 0, 0);

            point = transaction.CurrentPoint;

            _log.Info($"CalcMaxPointCanUse user: {user.UserId} | point: {point}");

            var (configError, exchangePointDto) = ValidateAndGetExchangePointConfig(user.ShopId);

            if (configError != null) return (configError, 0, 0);
            if (!exchangePointDto.DiscountJson.Status) return ("Đã tắt giảm giá mua sắm", 0, 0);

            var (ratePointError, ratePoint) = GetRatePointFromConfig(user.ShopId, true);
            if (ratePointError != null) return (ratePointError, 0, 0);

            var isPointLimitEnabled = exchangePointDto.DiscountJson.IsScore.IsValue;
            var maxPercentPoint = exchangePointDto.DiscountJson.MaxPercent;

            long priceToPoint = (long)(totalPrice * maxPercentPoint) / 100 / ratePoint;
            int availablePoint = (int)(point < priceToPoint ? point : priceToPoint);

            _log.Info($"CalcMaxPointCanUse ratePoint: {ratePoint} | maxPercentPoint: {maxPercentPoint} | isPointLimitEnabled: {isPointLimitEnabled}  totalPrice: {totalPrice}");
            _log.Info($"CalcMaxPointCanUse user.Point: {point} | priceToPoint: {priceToPoint} | availablePoint: {availablePoint}");

            if (!isPointLimitEnabled)
            {
                var limitPoint = exchangePointDto.DiscountJson.IsScore.Value;
                var pointLimitType = exchangePointDto.DiscountJson.IsScore.OptionDate;
                var (startDate, endDate) = GetTimeRange(pointLimitType);

                PointTransactionFilterDto objRequest = new PointTransactionFilterDto()
                {
                    ShopId = user.ShopId,
                    TypeTransaction = TypeTransaction.Discount,
                    UserId = userId,
                    IsAdditionEnabled = false,
                    FromDate = startDate,
                    ToDate = endDate
                };

                var exhangePointTransactions = _membershipLevelRepository.ListPointTransactionBy(objRequest);

                var totalPointUsed = exhangePointTransactions.Sum(x => Math.Abs(x.PointsEarned));

                _log.Info($"CalcMaxPointCanUse totalPointUsed: {totalPointUsed} | limitPoint: {limitPoint}");

                if (totalPointUsed >= limitPoint)
                    return ($"Đã đạt giới hạn sử dụng điểm: ({limitPoint} điểm) trong {GetTimeRangeText(pointLimitType)}", 0, 0);

                int remainAvailablePoint = limitPoint - totalPointUsed;
                if (remainAvailablePoint < availablePoint) availablePoint = remainAvailablePoint;
                return (null, availablePoint, ratePoint);
            }
            return (null, availablePoint, ratePoint);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tính điểm tối đa có thể dùng: {ex.Message}", ex);
            return ($"Đã xảy ra lỗi: {ex.Message}", 0, 0);
        }
    }

    public async Task<(string? errors, int point)> CalcPointGainAfterComplete(string cartId)
    {
        try
        {
            Cart? cart = _cartRepository.FindByCartId(cartId);
            if (cart == null) return ("Không tìm thấy giỏ hàng", 0);

            var (userError, user) = ValidateUserAndConfig(cart.UserId, cart.ShopId);
            if (userError != null) return (userError, 0);

            var (levelError, updatedUser) = ValidateAndUpdateFreeMembershipLevel(user);
            if (levelError != null) return (levelError, 0);

            var (membershipError, userMembershipLevel) = ValidateUserMembershipLevel(updatedUser.MembershipLevelId);
            if (membershipError != null) return (membershipError, 0);

            var (configError, earnPointDto) = ValidateAndGetEarnPointConfig(cart.ShopId, null);
            if (configError != null) return (configError, 0);
            if (!earnPointDto.OrderJson.Status) return ("Đã tắt tích điểm khi mua hàng", 0);

            var (ratePointError, ratePoint) = GetRatePointFromConfig(cart.ShopId);
            if (ratePointError != null) return (ratePointError, 0);

            var (_, newPoint) = CalculatePoints(
                cart.TotalAfterTax,
                userMembershipLevel.PointRate,
                earnPointDto.OrderJson.IsScore,
                ratePoint,
                cart.TransportPrice,
                cart.VoucherTransportPrice
            );

            return (null, newPoint);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tính điểm cho giỏ hàng {cartId}: {ex.Message}", ex);
            return ($"Đã xảy ra lỗi: {ex.Message}", 0);
        }
    }

    public async Task RefundPointToUser(Order order)
    {
        if (order.Creator == null) return;
        try
        {
            //Hoàn lại số điểm đã dùng để giảm giá đơn hàng
            if (order.ExchangePoints > 0)
            {
                //tìm point transaction số điểm đã dùng để giam giá đơn hàng
                var pointUseToDiscountOrder = _membershipLevelRepository.FindPointTransactionBy(shopId: order.ShopId, typeTransaction: TypeTransaction.Discount, userId: order.Creator.UserId, orderId: order.OrderId);
                if (pointUseToDiscountOrder != null)
                {
                    //đánh dấu transaction đó đã được hoàn điểm
                    pointUseToDiscountOrder.IsRefund = true;
                    await _membershipLevelRepository.UpdatePointTransaction(pointUseToDiscountOrder);
                }

                //Tạo transaction mới hoàn điểm cho user
                var objPointTransaction = new PointTransaction
                {
                    ShopId = order.ShopId,
                    UserId = order.Creator.UserId,
                    PointsEarned = order.ExchangePoints ?? 0,
                    IsAdditionEnabled = false,
                    Type = TypeTransaction.PointRefund,
                    Note = $"Hoàn điểm do {(order.StatusOrder == TypeOrderStatus.Failed ? "hủy" : "hoàn")} đơn hàng #{order.OrderNo}",
                    Detail = $"Hoàn điểm do {(order.StatusOrder == TypeOrderStatus.Failed ? "hủy" : "hoàn")} đơn hàng #{order.OrderNo}",
                    OrderId = order.OrderId
                };

                await UpdateUserPoint(objPointTransaction);

            }

            var pointsEarnedWhenOrderComplete = _membershipLevelRepository.FindPointTransactionBy(shopId: order.ShopId, orderId: order.OrderId, typeTransaction: TypeTransaction.Shop, userId: order.Creator.UserId);

            //trừ điểm user nếu đơn hàng được cộng điểm trước đó
            if (pointsEarnedWhenOrderComplete != null)
            {
                var objPointTransaction = new PointTransaction
                {
                    ShopId = order.ShopId,
                    UserId = order.Creator.UserId,
                    PointsEarned = -pointsEarnedWhenOrderComplete.PointsEarned,
                    IsAdditionEnabled = false,
                    Type = TypeTransaction.Adjust,
                    Note = $"Trừ điểm do hủy/hoàn đơn #{order.OrderNo}",
                    Detail = $"Trừ điểm do hủy/hoàn đơn #{order.OrderNo}",
                    OrderId = order.OrderId
                };

                await UpdateUserPoint(objPointTransaction);
            }

            if (order.StatusOrder == TypeOrderStatus.Refund)
            {
                int priceToPoint = (int)order.Price / Constants.RatePoint;
                if (priceToPoint > 0)
                {
                    var objPointTransaction = new PointTransaction
                    {
                        ShopId = order.ShopId,
                        UserId = order.Creator.UserId,
                        PointsEarned = priceToPoint,
                        IsAdditionEnabled = false,
                        Type = TypeTransaction.PointRefund,
                        Note = $"Hoàn tiền bằng điểm do hoàn đơn hàng #{order.OrderNo}",
                        Detail = $"Hoàn tiền bằng điểm do hoàn đơn hàng #{order.OrderNo}",
                        OrderId = order.OrderId
                    };

                    await UpdateUserPoint(objPointTransaction);
                }

            }

        }
        catch (Exception ex)
        {
            Logs.debug($"Đã xảy ra lỗi: {ex.Message}");
        }
    }
    #endregion

    #region Private methods

    private (DateTime? startDate, DateTime? endDate) GetTimeRange(OptionDate pointLimitType)
    {
        var now = DateTime.UtcNow;
        DateTime? startDate = null;
        DateTime? endDate = null;

        switch (pointLimitType)
        {
            case OptionDate.Day:
                startDate = now.Date;
                endDate = startDate.Value.AddDays(1);
                break;
            case OptionDate.Week:
                startDate = now.Date.AddDays(-(int)now.DayOfWeek);
                endDate = startDate.Value.AddDays(7);
                break;

            case OptionDate.Month:
                startDate = new DateTime(now.Year, now.Month, 1);
                endDate = startDate.Value.AddMonths(1);
                break;

        }
        return (startDate, endDate);

    }

    // Hàm lấy text mô tả khoảng thời gian
    private string GetTimeRangeText(OptionDate pointLimitType)
    {
        return pointLimitType switch
        {
            OptionDate.Day => "ngày",
            OptionDate.Week => "tuần",
            OptionDate.Month => "tháng",
            _ => "khoảng thời gian"
        };
    }

    private (string? error, User? user) ValidateUserAndConfig(string userId, string shopId)
    {
        var user = _userRepository.FindByUserId(userId);
        if (user == null) return ("Không tìm thấy user", null);

        var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(shopId);
        if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
        if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);

        return (null, user);
    }

    private (string? error, EarnPointDto? earnPointDto) ValidateAndGetEarnPointConfig(string shopId, string configJson)
    {
        var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(shopId);
        if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
        if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);

        var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);
        if (earnPointDto == null) return ("Không thể parse config earn point", null);

        return (null, earnPointDto);
    }

    private (string? error, ExchangePointsDto? exchangePointDto) ValidateAndGetExchangePointConfig(string shopId)
    {
        var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(shopId);
        if (configMembershipLevel == null) return ("Không tìm thấy config membership", null);
        if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", null);

        var exchangePointDto = JsonConvert.DeserializeObject<ExchangePointsDto>(configMembershipLevel.ExchangePoints);
        if (exchangePointDto == null) return ("Không thể parse config exchange point", null);

        return (null, exchangePointDto);
    }

    private (string? error, Order? order) ValidateOrder(string orderId)
    {
        var order = _orderRepository.FindByOrderId(orderId);
        if (order == null) return ("Không tìm thấy order", null);
        if (order.Creator?.UserId == null) return ("Không tích điểm cho đơn khách vãng lai", null);

        return (null, order);
    }

    private (string? error, User? user) ValidateReferralUser(string referrerCode, string shopId)
    {
        var user = _userRepository.FindByReferralCode(referrerCode, shopId);
        if (user == null) return ("Không tìm thấy user giới thiệu", null);

        return (null, user);
    }

    private (string? error, User? user) ValidateAndUpdateFreeMembershipLevel(User user)
    {
        var freeMemberShipLevel = _membershipLevelRepository.FindMemberShipLevelBySpendingThreshold(0, user.ShopId);
        if (user.MembershipLevelId == null && freeMemberShipLevel != null)
        {
            user.MembershipLevelId = freeMemberShipLevel.LevelId;
            user = _userRepository.UpdateUser(user);
        }

        if (user.MembershipLevelId == null)
        {
            return ("User không có membership level", null);
        }

        return (null, user);
    }

    private (string? error, MembershipLevel? level) ValidateUserMembershipLevel(string membershipLevelId)
    {
        var userMembershipLevel = _membershipLevelRepository.FindByMembershipId(membershipLevelId);
        if (userMembershipLevel == null) return ("Không tìm thấy membership level", null);

        return (null, userMembershipLevel);
    }

    private (string? error, int point) CalculatePoints(decimal price, int pointRate, TypePointCalculation isScore, int ratePoint, long transportPrice = 0, long voucherTransportPrice = 0)
    {
        if (isScore == TypePointCalculation.total_excluding_shipping)
        {
            price = price - (transportPrice - voucherTransportPrice);
        }

        int newPoint = (int)(price * pointRate) / 100;
        newPoint = newPoint / ratePoint;

        return (null, newPoint);
    }

    private (string? error, int ratePoint) GetRatePointFromConfig(string shopId, bool isExchangePoint = false)
    {
        try
        {
            var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(shopId);
            if (configMembershipLevel == null) return ("Không tìm thấy config membership", 0);
            if (!configMembershipLevel.Status) return ("Đã tắt chương trình sử dụng và tích lũy điểm", 0);

            if (isExchangePoint)
            {
                var exchangePointDto = JsonConvert.DeserializeObject<ExchangePointsDto>(configMembershipLevel.ExchangePoints);
                if (exchangePointDto == null) return ("Không thể parse config exchange point", 0);
                return (null, exchangePointDto.DiscountJson.Rule);
            }
            else
            {
                var exchangePointDto = JsonConvert.DeserializeObject<ExchangePointsDto>(configMembershipLevel.ExchangePoints);
                if (exchangePointDto == null) return ("Không thể parse config exchange point", 0);
                return (null, exchangePointDto.DiscountJson.Rule);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy rate point từ config: {ex.Message}", ex);
            return ($"Đã xảy ra lỗi: {ex.Message}", 0);
        }
    }
    #endregion

    public async Task<bool> UpdateUserPoint(PointTransaction objTransaction)
    {
        try
        {
            var configMembership = _membershipLevelRepository.GetConfigMembership(objTransaction.ShopId);
            if (configMembership == null) {
                _log.Info($"Config membership not found in this shop: {objTransaction.ShopId}");
                return false;
            }

            User user = _userRepository.FindByUserId(objTransaction.UserId);
            if (user == null) {
                _log.Info($"User not found in this shop: {objTransaction.UserId}");
                return false;
            }

            var oldPoint = _membershipLevelRepository.CalculateUserScoreDetails(user);
            if (!objTransaction.IsAdditionEnabled && oldPoint.CurrentPoint < objTransaction.PointsEarned)
            {
                _log.Info($"Insufficient points to deduct: {objTransaction.UserId}");
                return false;
            }
       
            var newPoint = oldPoint.CurrentPoint;
            if (objTransaction.IsAdditionEnabled)
                newPoint += Math.Abs(objTransaction.PointsEarned);
            else
                newPoint -= Math.Abs(objTransaction.PointsEarned);

            await _userRepository.UpdateUserPoint(user.UserId, newPoint);

            objTransaction.Expired = false;
            objTransaction.ExpiredAt = CalculateExpiredAt(configMembership);
            
            await _membershipLevelRepository.UpdatePointTransaction(objTransaction);

            return true;
        }
        catch (Exception ex)
        {
            _log.Error($"Error updating user point: {ex.Message}", ex);
            return false;
        }
    }

    private DateTime? CalculateExpiredAt(ConfigMembershipLevel configMembership)
    {
        if (configMembership.ValidUntil == TypeValidUntil.Days180) {
            var expiredAt = DateTimes.Now().AddDays(180);
            return expiredAt;
        }
        else if (configMembership.ValidUntil == TypeValidUntil.Year1) {
            var expiredAt = DateTimes.Now().AddYears(1);
            return expiredAt;
        }
        else if (configMembership.ValidUntil == TypeValidUntil.Year2) {
            var expiredAt = DateTimes.Now().AddYears(2);
            return expiredAt;
        }

        return null;
    }
}
