using System;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;

using log4net;

namespace App.ECommerce.ProcessFlow.Implement;

public class MembershipLevelFlow : IMembershipLevelFlow
{
    private readonly ILog _log = LogManager.GetLogger(typeof(CalcMemberLevelFlow));
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IUserRepository _userRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;

    public MembershipLevelFlow(
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IUserRepository userRepository,
        IMembershipLevelRepository membershipLevelRepository
    )
    {
        _userRepository = userRepository;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _membershipLevelRepository = membershipLevelRepository;
    }

    public async Task DeleteMembershipLevel(string levelId)
    {
        try
        {
            MembershipLevel? items = _membershipLevelRepository.DeleteMembership(levelId);
            var users = _userRepository.ListUserByMembershipLevelId(levelId, items.ShopId);

            foreach (var user in users)
            {
                user.MembershipLevelId = null;
                _userRepository.UpdateUser(user);
                await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
            }
            if (items != null)
            {
                if (items.Image?.Link != null)
                    await S3Upload.DeleteImageS3(new List<string>() { items.Image.Link });

                _membershipLevelRepository.DeleteMembership(items.LevelId);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"Error deleting membership level {levelId}: {ex.Message}", ex);
        }
    }

    public async Task ActionAfterUpdateMembershipLevel(string levelId)
    {
        try
        {
            MembershipLevel? items = _membershipLevelRepository.FindByMembershipId(levelId);
            // Lấy tất cả người dùng
            var users = _userRepository.ListAllUsers(items.ShopId);

            foreach (var user in users)
            {
                await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"Error updating membership level {levelId}: {ex.Message}", ex);
        }
    }
}
