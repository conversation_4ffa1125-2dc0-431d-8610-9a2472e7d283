using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;

namespace App.ECommerce.ProcessFlow.Implement;

public class TempFilesFlow : ITempFilesFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(TempFilesFlow));
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly IStorageRepository _storageRepository;
    private readonly ITempFilesRepository _tempFilesRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly ICategoryRepository _categoryRepository;

    public TempFilesFlow(
        IGroupFileRepository groupFileRepository,
        IStorageRepository storageRepository,
        ITempFilesRepository tempFilesRepository,
        IItemsRepository itemsRepository,
        ICategoryRepository categoryRepository
    )
    {
        _groupFileRepository = groupFileRepository;
        _storageRepository = storageRepository;
        _tempFilesRepository = tempFilesRepository;
        _itemsRepository = itemsRepository;
        _categoryRepository = categoryRepository;
    }

    #region  Download File
    public async Task ProcessTempFiles()
    {
        try
        {
            var tempFiles = await _tempFilesRepository.GetListAsync(RefTypeEnum.Product, BaseStatusEnum.Pending);

            if (!tempFiles.Any())
                return;

            // Parallel xử lý nhiều temp files cùng lúc với giới hạn concurrency
            var semaphore = new SemaphoreSlim(5, 5); // Giới hạn 5 tasks chạy đồng thời
            var tasks = tempFiles.Select(async obj =>
            {
                await semaphore.WaitAsync();
                try
                {
                    await ProcessSingleTempFile(obj);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
        }   
        catch (Exception ex)
        {
            _log.Error($"Error in ProcessTempFiles: {ex.Message}", ex);
        }
    }

    private async Task ProcessSingleTempFile(TempFiles obj)
    {
        try
        {
            // Cập nhật status thành Processing trước khi xử lý
            obj.Status = BaseStatusEnum.Processing;
            await _tempFilesRepository.UpdateAsync(obj);

            string prefixName = "uploads";
            
            switch (obj.RefType)
            {
                case RefTypeEnum.Product:
                    prefixName = "product";
                    break;
                case RefTypeEnum.Service:
                    prefixName = "service";
                    break;
            }

            var images = await ProcessDownloadImage(prefixName, obj);

            if (images != null && images.Any())
            {
                switch (obj.RefType)
                {
                    case RefTypeEnum.Product:
                        await _itemsRepository.UpdateImages(obj.ShopId, obj.RefId, images);
                        break;
                    case RefTypeEnum.Service:
                        await _itemsRepository.UpdateImages(obj.ShopId, obj.RefId, images);
                        break;
                    case RefTypeEnum.CategoryProduct:
                        await _categoryRepository.UpdateImages(obj.ShopId, obj.RefId, images.FirstOrDefault());
                        break;
                    case RefTypeEnum.CategoryService:
                        await _categoryRepository.UpdateImages(obj.ShopId, obj.RefId, images.FirstOrDefault());
                        break;
                }
            }

            await _tempFilesRepository.DeleteAsync(obj.TempFileId);
        }
        catch (Exception ex)
        {
            _log.Error($"Error processing temp file {obj.TempFileId}: {ex.Message}", ex);
            
            // Cập nhật status thành Failed nếu có lỗi
            try
            {
                obj.Status = BaseStatusEnum.Failed;
                await _tempFilesRepository.UpdateAsync(obj);
            }
            catch (Exception updateEx)
            {
                _log.Error($"Error updating temp file status to Failed: {updateEx.Message}", updateEx);
            }
        }
    }

    private async Task<List<MediaInfo>> ProcessDownloadImage(string prefixName, TempFiles obj)
    {
        try
        {
            if (obj.URLs == null || !obj.URLs.Any())
                return new List<MediaInfo>();

            // Parallel download nhiều URLs cùng lúc với giới hạn concurrency
            var semaphore = new SemaphoreSlim(10, 10); // Giới hạn 10 downloads đồng thời
            var downloadTasks = obj.URLs.Select(async url =>
            {
                await semaphore.WaitAsync();
                try
                {
                    return await DownloadSingleImage(url, prefixName);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var results = await Task.WhenAll(downloadTasks);
            var images = results.Where(img => img != null).ToList();

            return images;
        }
        catch (Exception ex)
        {
            _log.Error($"Error in ProcessDownloadImage for temp file {obj.TempFileId}: {ex.Message}", ex);
            return new List<MediaInfo>();
        }
    }

    private async Task<MediaInfo> DownloadSingleImage(string url, string prefixName)
    {
        try
        {
            UploadStorageDto objUpload = new UploadStorageDto
            {
                URL = url,
                PrefixPath = ExportConst.PATH_IMPORT,
                PrefixName = prefixName,
                ContentType = "image/jpeg"
            };

            MediaFile objMedia = await _storageRepository.UploadImageUrlAsync(objUpload);

            if (objMedia != null)
            {
                return new MediaInfo
                {
                    Type = TypeMedia.IMAGE,
                    Link = objMedia.Link
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _log.Error($"Error downloading image from URL {url}: {ex.Message}", ex);
            return null;
        }
    }
    #endregion

    #region  Create Temp Files
    public async Task<TempFiles?> CreateTempFilesForImages(string shopId, string refId, RefTypeEnum refType, List<MediaInfo> images)
    {
        try 
        {
            if (images == null || !images.Any())
                return null;

            var externalUrls = images.Where(x => !x.Link.StartsWith(S3Upload.GetDomainS3())).Select(x => x.Link).ToList();
            
            if (!externalUrls.Any())
                return null;

            var objTempFile = new TempFiles
            {
                ShopId = shopId,
                RefId = refId,
                RefType = refType,
                Status = BaseStatusEnum.Pending,
                URLs = externalUrls
            };

            return await _tempFilesRepository.CreateAsync(objTempFile);
        }
        catch (Exception ex)
        {
            _log.Error($"Error creating temp files for images: {ex.Message}", ex);
            return null;
        }
    }

    public async Task<TempFiles?> CreateTempFilesForUrls(string shopId, string refId, RefTypeEnum refType, List<string> urls)
    {
        try 
        {
            if (urls == null || !urls.Any())
                return null;

            var objTempFile = new TempFiles
            {
                ShopId = shopId,
                RefId = refId,
                RefType = refType,
                Status = BaseStatusEnum.Pending,
                URLs = urls
            };

            return await _tempFilesRepository.CreateAsync(objTempFile);
        }
        catch (Exception ex)
        {
            _log.Error($"Error creating temp files for urls: {ex.Message}", ex);
            return null;
        }
    }
    #endregion

    public string GetBasePathByRefType(RefTypeEnum refType)
    {
        return refType switch
        {
            RefTypeEnum.Shop => "shops",
            RefTypeEnum.TriggerEvent => "events",
            RefTypeEnum.Campaign => "campaigns",
            RefTypeEnum.User => "users",
            RefTypeEnum.CategoryProduct => "categories/products",
            RefTypeEnum.Product => "products",
            RefTypeEnum.CategoryService => "categories/services",
            RefTypeEnum.Service => "services",
            RefTypeEnum.ZaloUID => "zalo",
            RefTypeEnum.Voucher => "vouchers",
            RefTypeEnum.ArticleCategory => "categories/articles",
            RefTypeEnum.Article => "articles",
            RefTypeEnum.MembershipPoint => "membership",
            RefTypeEnum.PopupAds => "popup-ads",
            _ => "others"
        };
    }
}
