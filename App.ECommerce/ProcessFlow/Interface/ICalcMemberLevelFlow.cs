using System;
using App.ECommerce.Repository.Entities;

namespace App.ECommerce.ProcessFlow.Interface;

public interface ICalcMemberLevelFlow
{
    /// <summary>
    /// Tích điểm user sau khi mua hàng
    /// </summary>
    Task<(string? errors, User? user)> CalcUserPointByOrder(string orderId);

    /// <summary>
    /// Tính điểm user sau khi đăng ký
    /// </summary>
    Task<(string? errors, User? user)> CalcUserPointBySignUp(string userId);

    /// <summary>
    /// Tính điểm user sau khi chia sẻ
    /// </summary>
    Task<(string? errors, User? user)> CalcUserPointByShare(string referrerUserId);

    /// <summary>
    /// Tính điểm user người được giới thiệu đặt hàng (A giới thiệu B, B đặt đơn hàng, orderId là id đơn của B)
    /// </summary>
    Task<(string? errors, User? user)> CalcUserPointsForReferralOrder(string orderId);

    /// <summary>
    /// Tính level user sau khi đơn hàng hoàn thành
    /// </summary>
    Task<(string? errors, User? user)> CalcUserLevel(string userId);

    /// <summary>
    /// Tính điểm tối đa có thể sử dụng cho đơn hàng
    /// </summary>
    Task<(string? errors, int point, long pointPrice)> CalcMaxPointCanUse(long totalPrice, string userId);

    /// <summary>
    /// Tính số điểm đạt được sau khi hoàn thành đơn
    /// </summary>
    Task<(string? errors, int point)> CalcPointGainAfterComplete(string cartId);

    /// <summary>
    /// Hoàn điểm cho user khi hủy đơn
    /// </summary>
    Task RefundPointToUser(Order order);

    /// <summary>
    /// Cập nhật điểm cho user
    /// </summary>
    Task<bool> UpdateUserPoint(PointTransaction transaction);
}
