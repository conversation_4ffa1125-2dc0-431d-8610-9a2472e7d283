﻿[
  // FILE
  {
    "Localized_Key": "FILE_UPLOAD_NOT_FOUND",
    "Localized_Value": "Upload file not found"
  },
  {
    "Localized_Key": "FILE_UPLOAD_NAME_INVALID",
    "Localized_Value": "Invalid file name"
  },
  {
    "Localized_Key": "FILE_UPLOAD_MAX_SIZE",
    "Localized_Value": "File size exceeds the allowed limit"
  },
  // CATEGORY
  {
    "Localized_Key": "CATEGORY_INVALID",
    "Localized_Value": "Invalid category"
  },
  {
    "Localized_Key": "SUB_CATEGORY_INVALID",
    "Localized_Value": "Invalid subcategory"
  },

  // BaseController
  {
    "Localized_Key": "BASE_LANGUAGE",
    "Localized_Value": "EN"
  },
  {
    "Localized_Key": "BASE_NOT_SUPPORT_PLATFORM",
    "Localized_Value": "Not supported on this platform"
  },
  {
    "Localized_Key": "BASE_INPUT_NOT_VALUE",
    "Localized_Value": "The input was not valid"
  },
  {
    "Localized_Key": "BASE_USER_AUTH_NOT_FOUND",
    "Localized_Value": "User authen not found"
  },
  {
    "Localized_Key": "BASE_USER_NOT_FOUND",
    "Localized_Value": "User not found"
  },
  {
    "Localized_Key": "BASE_USER_AUTH_NOT_REGISTRY_WITH_COMPANY",
    "Localized_Value": "User not registry with this company"
  },
  {
    "Localized_Key": "BASE_COMPANY_NOT_FOUND",
    "Localized_Value": "Company not found"
  },
  {
    "Localized_Key": "BASE_EMPLOYEE_NOT_FOUND",
    "Localized_Value": "Employee not found"
  },
  {
    "Localized_Key": "BASE_EXCEPTION",
    "Localized_Value": "Exceptions appeared on the system. We will take care of it soon"
  },
  {
    "Localized_Key": "BASE_REQUIRED_VALUE",
    "Localized_Value": "Field can not blank"
  },
  {
    "Localized_Key": "BASE_NO_DATA",
    "Localized_Value": "No data"
  },
  {
    "Localized_Key": "BASE_DATE_FORMAT_INCORRECT",
    "Localized_Value": "Incorrect date format"
  },
  {
    "Localized_Key": "BASE_MUST_NUMBER",
    "Localized_Value": "Must be a number"
  },
  {
    "Localized_Key": "BASE_MUST_NUMBER_POSITIVE",
    "Localized_Value": "Must be a positive number"
  },
  {
    "Localized_Key": "BASE_MUST_NUMBER_GREATER_0",
    "Localized_Value": "Must be a number greater than 0"
  },

  // AuthController
  {
    "Localized_Key": "AUTH_INVALID_TOKEN",
    "Localized_Value": "Id_token is invalid"
  },
  {
    "Localized_Key": "AUTH_NOT_GRANT_PERMISSION",
    "Localized_Value": "Login google not grant permission profile scope email, profile"
  },
  {
    "Localized_Key": "AUTH_INVALID_EMAIL",
    "Localized_Value": "Email is invalid"
  },
  {
    "Localized_Key": "AUTH_INVALID_FIRSTNAME",
    "Localized_Value": "FirstName is invalid"
  },
  {
    "Localized_Key": "AUTH_INVALID_LASTNAME",
    "Localized_Value": "LastName is invalid"
  },
  {
    "Localized_Key": "AUTH_EMAIL_REGISTERED_WEBSITE",
    "Localized_Value": "The email was registered by account"
  },
  {
    "Localized_Key": "AUTH_REGISTERED_FAILED",
    "Localized_Value": "Registration failed"
  },
  {
    "Localized_Key": "AUTH_EMAIL_REGISTERED_USER",
    "Localized_Value": "Email is already used by orther user"
  },
  {
    "Localized_Key": "AUTH_PHONE_REGISTERED_USER",
    "Localized_Value": "Phone is already used by orther user"
  },
  {
    "Localized_Key": "AUTH_USER_NOT_APPROVE",
    "Localized_Value": "This account has not been approved"
  },
  {
    "Localized_Key": "AUTH_USER_NOT_REGISTERED",
    "Localized_Value": "Account not registered"
  },
  {
    "Localized_Key": "AUTH_USER_LOGIN_NOT_APPROVE",
    "Localized_Value": "This account is being approved! Please login again later"
  },
  {
    "Localized_Key": "AUTH_USER_LOGIN_NOT_APPROVE",
    "Localized_Value": "This account is being reviewed! Please login again later"
  },
  {
    "Localized_Key": "AUTH_PASS_FAILD_KMS",
    "Localized_Value": "Password fail format KMS service"
  },
  {
    "Localized_Key": "AUTH_PASS_FAILD_MD5",
    "Localized_Value": "Incorrect phone number or password"
  },
  {
    "Localized_Key": "AUTH_LOGIN_ATTEMPT",
    "Localized_Value": "Invalid login or unapproved account"
  },
  {
    "Localized_Key": "AUTH_LOGIN_INACTIVE",
    "Localized_Value": "Account has been locked, please contact <NAME_EMAIL> to be reactivated"
  },
  {
    "Localized_Key": "AUTH_LOGIN_CAN_NOT_INACTIVE",
    "Localized_Value": "Account is not locked, password is invalid"
  },
  {
    "Localized_Key": "AUTH_LOGIN_EMPLOYEE_DEMO",
    "Localized_Value": "Invalid login with sample employee data"
  },
  {
    "Localized_Key": "AUTH_TYPE_NOT_SUPPORT_REGISTRY",
    "Localized_Value": "This method does not support registry"
  },
  {
    "Localized_Key": "AUTH_TYPE_NOT_SUPPORT_LOGIN",
    "Localized_Value": "This method does not support login"
  },
  {
    "Localized_Key": "AUTH_LOGIN_REQUIRED_2FA",
    "Localized_Value": "User require login with 2FA"
  },
  {
    "Localized_Key": "AUTH_INVALID_2FA_CODE",
    "Localized_Value": "Verification Code2FA is invalid"
  },
  {
    "Localized_Key": "AUTH_LOGIN_LOCKED",
    "Localized_Value": "User account locked out"
  },
  {
    "Localized_Key": "AUTH_LOGIN_INVALID",
    "Localized_Value": "Invalid username or password"
  },
  {
    "Localized_Key": "AUTH_TOKEN_NOT_FOUND",
    "Localized_Value": "Refresh token not found"
  },
  {
    "Localized_Key": "AUTH_TOKEN_EXPIRED",
    "Localized_Value": "Refresh token is expired"
  },
  {
    "Localized_Key": "AUTH_USER_NOT_FOUND",
    "Localized_Value": "User not found"
  },
  {
    "Localized_Key": "AUTH_USER_HAS_CONFIRMED_EMAIL",
    "Localized_Value": "User has confirmed email"
  },
  {
    "Localized_Key": "AUTH_CALLBACK_URL_NOT_FOUND",
    "Localized_Value": "Callback url not found"
  },
  {
    "Localized_Key": "AUTH_USER_CONFIRMED_EMAIL",
    "Localized_Value": "User has confirmed email"
  },
  {
    "Localized_Key": "AUTH_USER_CONFIRMED_INVALID_CODE",
    "Localized_Value": "A code must be supplied for confirming your email"
  },
  {
    "Localized_Key": "AUTH_INVALID_EMAIL_FORMAT",
    "Localized_Value": "Invalid email format"
  },
  {
    "Localized_Key": "AUTH_INVALID_PHONE_FORMAT",
    "Localized_Value": "Invalid phone format"
  },
  {
    "Localized_Key": "AUTH_INVALID_USERNAME_FORMAT",
    "Localized_Value": "Invalid username format"
  },
  {
    "Localized_Key": "AUTH_EMAIL_NOT_REGISTERED",
    "Localized_Value": "Email is not registered"
  },
  {
    "Localized_Key": "AUTH_LOGIN_PROVIDER_1",
    "Localized_Value": "This email registered by"
  },
  {
    "Localized_Key": "AUTH_LOGIN_PROVIDER_2",
    "Localized_Value": "Please login with"
  },
  {
    "Localized_Key": "AUTH_NOT_RECOVERY_PASS",
    "Localized_Value": "Can not recovery password for user registry by Google"
  },
  {
    "Localized_Key": "AUTH_CONFIRM_TOKEN_NOT_FOUND",
    "Localized_Value": "Token not found or expiration"
  },
  {
    "Localized_Key": "AUTH_ERROR_RESET_PASS",
    "Localized_Value": "User can not reset password"
  },
  {
    "Localized_Key": "AUTH_FINISH_RESET_PASS",
    "Localized_Value": "User does not exist or is not registered with the Evotech system"
  },
  {
    "Localized_Key": "AUTH_SUCCESS_RESET_PASS",
    "Localized_Value": "You have successfully reset your password"
  },
  {
    "Localized_Key": "AUTH_SUCCESS_DELETE",
    "Localized_Value": "Account deleted successfully"
  },
  {
    "Localized_Key": "AUTH_SUCCESS_UPDATE_GPS",
    "Localized_Value": "Location updated successfully"
  },
  {
    "Localized_Key": "AUTH_SUCCESS_CONFIRM",
    "Localized_Value": "Thank you for confirming your email"
  },
  {
    "Localized_Key": "AUTH_DEGREE_INVALID",
    "Localized_Value": "Degree invalid"
  },
  {
    "Localized_Key": "AUTH_FIRSTNAME_INVALID",
    "Localized_Value": "Firstname invalid"
  },
  {
    "Localized_Key": "AUTH_LASTNAME_INVALID",
    "Localized_Value": "Lastname invalid"
  },
  {
    "Localized_Key": "AUTH_NAME_INVALID",
    "Localized_Value": "Name invalid"
  },
  {
    "Localized_Key": "AUTH_PHONE_INVALID",
    "Localized_Value": "Phonenumber invalid"
  },
  {
    "Localized_Key": "AUTH_GENDER_INVALID",
    "Localized_Value": "Gender invalid"
  },
  {
    "Localized_Key": "AUTH_LANGUAGE_INVALID",
    "Localized_Value": "Language invalid"
  },
  {
    "Localized_Key": "AUTH_ROLE_INVALID",
    "Localized_Value": "Account does not have permission to use this function"
  },
  {
    "Localized_Key": "AUTH_PASS_INVALID",
    "Localized_Value": "Invalid password"
  },
  {
    "Localized_Key": "AUTH_PASSWORD_INVALID",
    "Localized_Value": "The password must be at least 8 characters long, including uppercase letters, lowercase letters, numbers, and special characters."
  },
  {
    "Localized_Key": "AUTH_PROVINCE_INVALID",
    "Localized_Value": "Province invalid"
  },
  {
    "Localized_Key": "AUTH_DISTRICT_INVALID",
    "Localized_Value": "District invalid"
  },
  {
    "Localized_Key": "AUTH_WARDS_INVALID",
    "Localized_Value": "Wards invalid"
  },
  {
    "Localized_Key": "AUTH_DISTRICT_NOT_FOUND",
    "Localized_Value": "District is not part of province"
  },
  {
    "Localized_Key": "AUTH_WARDS_NOT_FOUND",
    "Localized_Value": "Ward is not part of a district"
  },
  {
    "Localized_Key": "AUTH_NOT_PERMISSION",
    "Localized_Value": "This logged account has no permissions"
  },
  {
    "Localized_Key": "AUTH_OTP_EXCEED_PHONE",
    "Localized_Value": "Too many OTP submissions for this phone number"
  },
  {
    "Localized_Key": "AUTH_OTP_EXCEED_IP",
    "Localized_Value": "Too many OTP submissions from this address"
  },
  {
    "Localized_Key": "AUTH_OTP_INVALID_CODE",
    "Localized_Value": "Invalid OTP"
  },
  {
    "Localized_Key": "AUTH_OTP_EXPRIED_CODE",
    "Localized_Value": "Expired OTP"
  },
  {
    "Localized_Key": "AUTH_EMAIL_EXITS",
    "Localized_Value": "This email address is already in use"
  },
  {
    "Localized_Key": "AUTH_REMOVE_PASS_INVALID",
    "Localized_Value": "Please enter password"
  },
  {
    "Localized_Key": "AUTH_FULLNAME_REQUIRED",
    "Localized_Value": "Full name is required."
  },
  {
    "Localized_Key": "AUTH_LASTNAME_REQUIRED",
    "Localized_Value": "Please enter your last name."
  },
  {
    "Localized_Key": "AUTH_FIRSTNAME_REQUIRED",
    "Localized_Value": "Please enter your first name."
  },
  {
    "Localized_Key": "AUTH_FULLNAME_REQUIRED",
    "Localized_Value": "Please enter your full name."
  },
  {
    "Localized_Key": "AUTH_PHONE_REQUIRED",
    "Localized_Value": "Phone number is required."
  },
  {
    "Localized_Key": "AUTH_EMAIL_REQUIRED",
    "Localized_Value": "Email address is required."
  },
  {
    "Localized_Key": "AUTH_PHONE_FORMAT_INVALID",
    "Localized_Value": "Phone number format is invalid."
  },
  {
    "Localized_Key": "AUTH_EMAIL_FORMAT_INVALID",
    "Localized_Value": "Email address format is invalid."
  },
  {
    "Localized_Key": "AUTH_EMAIL_REGISTERED_PARTNER",
    "Localized_Value": "This email address is already in use."
  },
  {
    "Localized_Key": "AUTH_PHONE_REGISTERED_PARTNER",
    "Localized_Value": "This phone number is already in use."
  },
  {
    "Localized_Key": "PACKAGE_START_REQUIRED",
    "Localized_Value": "Please enter the package start date."
  },
  {
    "Localized_Key": "PACKAGE_END_REQUIRED",
    "Localized_Value": "Please enter the package end date."
  },
  {
    "Localized_Key": "PACKAGE_END_MUST_GREATER",
    "Localized_Value": "Package end date must be greater than start date."
  },
  {
    "Localized_Key": "PARTNER_CREATE_SUCCESS",
    "Localized_Value": "Partner created successfully."
  },
  {
    "Localized_Key": "PARTNER_EDIT_SUCCESS",
    "Localized_Value": "Partner edited successfully."
  },
  {
    "Localized_Key": "PARTNER_INITIAL_DEPOSIT",
    "Localized_Value": "Initial deposit."
  },

  // ProfileController
  {
    "Localized_Key": "PRO_ERROR_UPDATE",
    "Localized_Value": "Can not update profile"
  },
  {
    "Localized_Key": "PRO_HAS_CONFIG_2FA",
    "Localized_Value": "User has configured 2-step authentication"
  },
  {
    "Localized_Key": "PRO_INVALID_CODE_2FA",
    "Localized_Value": "Verification code is invalid"
  },
  {
    "Localized_Key": "PRO_SUCCESS_VERIFY_2FA",
    "Localized_Value": "User has enabled 2FA with an authenticator app"
  },
  {
    "Localized_Key": "PRO_SUCCESS_REMOVE_2FA",
    "Localized_Value": "Your authenticator app key has been reset, you will need to configure your authenticator app using the new key"
  },
  {
    "Localized_Key": "PRO_INVALID_OLDPASS",
    "Localized_Value": "Old password is invalid"
  },
  {
    "Localized_Key": "PRO_OLDPASS_INCORRECT",
    "Localized_Value": "Old password is incorrect"
  },
  {
    "Localized_Key": "PRO_INVALID_NEWPASS",
    "Localized_Value": "New password is invalid"
  },
  {
    "Localized_Key": "PRO_NEWPASS_INCORRECT",
    "Localized_Value": "New password at least 8 characters and at most 20 characters"
  },
  {
    "Localized_Key": "INVALID_OR_EXPIRED_TOKEN",
    "Localized_Value": "Invalid or expired token"
  },
  {
    "Localized_Key": "TOKEN_AND_NEW_PASSWORD_REQUIRED",
    "Localized_Value": "Token and new password are required"
  },
  {
    "Localized_Key": "PRO_ERROR_CHANGE_PASS",
    "Localized_Value": "Can not change password"
  },
  {
    "Localized_Key": "PRO_ERROR_CHANGE_PHONE",
    "Localized_Value": "Cannot change phone number"
  },
  {
    "Localized_Key": "PRO_ERROR_CHANGE_EMAIL",
    "Localized_Value": "Cannot change email number"
  },
  {
    "Localized_Key": "PASSWORD_MIN_LENGTH",
    "Localized_Value": "Password must be at least 8 characters"
  },
  {
    "Localized_Key": "PASSWORD_REQUIRE_LOWERCASE",
    "Localized_Value": "Password must contain at least one lowercase letter"
  },
  {
    "Localized_Key": "PASSWORD_REQUIRE_UPPERCASE",
    "Localized_Value": "Password must contain at least one uppercase letter"
  },
  {
    "Localized_Key": "PASSWORD_REQUIRE_NUMBER",
    "Localized_Value": "Password must contain at least one number"
  },
  {
    "Localized_Key": "PASSWORD_REQUIRE_SPECIAL",
    "Localized_Value": "Password must contain at least one special character"
  },
  {
    "Localized_Key": "PASSWORD_CONFIRM_MISMATCH",
    "Localized_Value": "Password confirmation does not match"
  },
  // Email
  {
    "Localized_Key": "EMAIL_NEW_ACCOUNT_SUBJECT",
    "Localized_Value": "Welcome to Evotech"
  },
  {
    "Localized_Key": "EMAIL_NEW_ACCOUNT_TITLE",
    "Localized_Value": "Register a new account"
  },
  {
    "Localized_Key": "EMAIL_NEW_ACCOUNT_MESSAGE",
    "Localized_Value": "Evotech would like to thank you for trusting to use our service. We are pleased to serve and fulfill your needs. <br><strong>Once you click on the link below, your account will be activated.</strong>"
  },
  {
    "Localized_Key": "EMAIL_NEW_ACCOUNT_BUTTON",
    "Localized_Value": "Confirm email"
  },
  {
    "Localized_Key": "EMAIL_CHANGE_PASS_SUBJECT",
    "Localized_Value": "Change Password"
  },
  {
    "Localized_Key": "EMAIL_CHANGE_PASS_TITLE",
    "Localized_Value": "Your account password has just been changed"
  },
  {
    "Localized_Key": "EMAIL_CHANGE_PASS_MESSAGE",
    "Localized_Value": "Someone just logged in and changed your password. We send you this email to make sure the person is you."
  },
  {
    "Localized_Key": "EMAIL_CHANGE_PASS_BUTTON",
    "Localized_Value": "Check operation"
  },
  {
    "Localized_Key": "EMAIL_RESET_PASS_SUBJECT",
    "Localized_Value": "Reset password request"
  },
  {
    "Localized_Key": "EMAIL_RESET_PASS_TITLE",
    "Localized_Value": "Confirm password recovery from Evotech"
  },
  {
    "Localized_Key": "EMAIL_RESET_PASS_MESSAGE",
    "Localized_Value": "Thank you for use Evotech. Once you click on the link below, you can recover your password. You will be redirected to enter your new password once again.<br><strong>Please note that you should not give out a password to anyone</strong>"
  },
  {
    "Localized_Key": "EMAIL_RESET_PASS_BUTTON",
    "Localized_Value": "Recovery Password"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_VERIFY_EMPLOYEE",
    "Localized_Value": "Confirm employee registration from Evotech"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_REGISTRATION_EMPLOYEE",
    "Localized_Value": "Thank you for registration"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_PAYROLL_1",
    "Localized_Value": "Pay period from"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_PAYROLL_2",
    "Localized_Value": "Payment Date"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_INVITE_EMPLOYEE_1",
    "Localized_Value": "has invited you to access their Evotech account"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_INVITE_EMPLOYEE_2",
    "Localized_Value": "You have received this email from Evotech as"
  },
  {
    "Localized_Key": "EMAIL_SUBJECT_INVITE_EMPLOYEE_3",
    "Localized_Value": "has invited you to access their Evotech account."
  },
  {
    "Localized_Key": "EMAIL_TEXT_1",
    "Localized_Value": "View our web Evotech"
  },
  {
    "Localized_Key": "EMAIL_TEXT_2",
    "Localized_Value": "if this email isn't displaying well."
  },
  {
    "Localized_Key": "EMAIL_TEXT_3",
    "Localized_Value": "Welcome To Evotech"
  },
  {
    "Localized_Key": "EMAIL_TEXT_4",
    "Localized_Value": "Link Application"
  },
  {
    "Localized_Key": "EMAIL_TEXT_6",
    "Localized_Value": "The Evotech Platform:"
  },
  {
    "Localized_Key": "EMAIL_TEXT_7",
    "Localized_Value": "Monitor your health<br>Telephone booking<br>Home health care<br>Prescription drug service<br>On-demand testing service<br>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_8",
    "Localized_Value": "You need to contact us ?"
  },
  {
    "Localized_Key": "EMAIL_TEXT_9",
    "Localized_Value": "For more information, you can login to the website"
  },
  {
    "Localized_Key": "EMAIL_TEXT_11",
    "Localized_Value": "Evotech<br>The Evotech Team<br>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_12",
    "Localized_Value": "Thank you for signing up to use Evotech. Once you click on the link below, your account will become active. You will be redirected to enter your username and password once again.<br><strong>Please note that your username is your email address and the password is the one you set at signup.</strong>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_16",
    "Localized_Value": "Thank you for signing up to use Evotech. Once you click on the link below, your account will be activated.<br><strong>Please note that your username is your email address and the password will be sent about email when you activate successfully.</strong>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_17",
    "Localized_Value": "Verify email"
  },
  {
    "Localized_Key": "EMAIL_TEXT_18",
    "Localized_Value": "Your Evotech for period"
  },
  {
    "Localized_Key": "EMAIL_TEXT_19",
    "Localized_Value": "can be viewed, printed or download as pdf from the attachment below."
  },
  {
    "Localized_Key": "EMAIL_TEXT_20",
    "Localized_Value": "Evotech, Thanks you !"
  },
  {
    "Localized_Key": "EMAIL_TEXT_21",
    "Localized_Value": "<strong>If you already have an account, please login at<br></strong>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_22",
    "Localized_Value": "<strong>If you don't have an account, please sign up at<br></strong>"
  },
  {
    "Localized_Key": "EMAIL_TEXT_23",
    "Localized_Value": "Login Now"
  },
  {
    "Localized_Key": "EMAIL_TEXT_24",
    "Localized_Value": "Registry Now"
  },
  {
    "Localized_Key": "EMAIL_TEXT_25",
    "Localized_Value": "Dear"
  },
  {
    "Localized_Key": "EMAIL_TEXT_26",
    "Localized_Value": "Note: the password attached is"
  },
  {
    "Localized_Key": "EMAIL_TEXT_29",
    "Localized_Value": "Thank you for signing up to use Evotech. <br> <strong>Your default password is: </strong> "
  },
  {
    "Localized_Key": "EMAIL_TEXT_30",
    "Localized_Value": "Login now"
  },
  // ShopPartnerController
  {
    "Localized_Key": "SHOP_LIMIT_CREATE",
    "Localized_Value": "Account is limited to create new shop"
  },
  {
    "Localized_Key": "SHOP_NOT_FOUND",
    "Localized_Value": "Shop not found"
  },
  {
    "Localized_Key": "SHOP_NOT_YOURS",
    "Localized_Value": "This shop is not yours"
  },
  {
    "Localized_Key": "SHOP_DELETE_SUCCESS",
    "Localized_Value": "Shop deleted successfully"
  },
  // AdvertiseController
  {
    "Localized_Key": "ADVERTISE_NOT_FOUND",
    "Localized_Value": "Ad not found"
  },
  {
    "Localized_Key": "ADVERTISE_NOT_YOURS",
    "Localized_Value": "This ad is not yours"
  },
  {
    "Localized_Key": "ADVERTISE_DELETE_SUCCESS",
    "Localized_Value": "Ad deleted successfully"
  },
  // ArticleController
  {
    "Localized_Key": "ARTICLE_CATEGORY_NOT_FOUND",
    "Localized_Value": "Article category not found"
  },
  {
    "Localized_Key": "ARTICLE_CATEGORY_NOT_YOURS",
    "Localized_Value": "This article category is not yours"
  },
  {
    "Localized_Key": "ARTICLE_CATEGORY_DELETE_SUCCESS",
    "Localized_Value": "Article category deleted successfully"
  },
  {
    "Localized_Key": "ARTICLE_NOT_FOUND",
    "Localized_Value": "Article not found"
  },
  {
    "Localized_Key": "ARTICLE_NOT_YOURS",
    "Localized_Value": "This article is not yours"
  },
  {
    "Localized_Key": "ARTICLE_DELETE_SUCCESS",
    "Localized_Value": "Article deleted successfully"
  },
  // BranchController
  {
    "Localized_Key": "BRANCH_NOT_FOUND",
    "Localized_Value": "Branch not found"
  },
  {
    "Localized_Key": "BRANCH_DELETE_SUCCESS",
    "Localized_Value": "Branch deleted successfully"
  },
  // CategoryPartnerController
  {
    "Localized_Key": "CATEGORY_NOT_FOUND",
    "Localized_Value": "Category not found"
  },
  {
    "Localized_Key": "CATEGORY_NOT_YOURS",
    "Localized_Value": "This category is not yours"
  },
  {
    "Localized_Key": "CATEGORY_DELETE_SUCCESS",
    "Localized_Value": "Category deleted successfully"
  },
  {
    "Localized_Key": "CATEGORY_PARENT_INVALID",
    "Localized_Value": "Invalid parent category"
  },
  {
    "Localized_Key": "CATEGORY_PARENT_TYPE_INVALID",
    "Localized_Value": "Invalid parent category type"
  },
  {
    "Localized_Key": "CATEGORY_NAME_INVALID",
    "Localized_Value": "Invalid category name"
  },

  // FileManageController
  {
    "Localized_Key": "FM_GROUP_NOT_FOUND",
    "Localized_Value": "Group not found"
  },
  {
    "Localized_Key": "FM_GROUP_NOT_YOURS",
    "Localized_Value": "This group is not yours"
  },
  {
    "Localized_Key": "FM_GROUP_DELETE_SUCCESS",
    "Localized_Value": "Group deleted successfully"
  },
  {
    "Localized_Key": "FM_FILE_NOT_FOUND",
    "Localized_Value": "File not found"
  },
  {
    "Localized_Key": "FM_FILE_NOT_YOURS",
    "Localized_Value": "This file is not yours"
  },
  {
    "Localized_Key": "FM_FILE_DELETE_SUCCESS",
    "Localized_Value": "File deleted successfully"
  },
  {
    "Localized_Key": "FM_FILE_MAX_COUNT",
    "Localized_Value": "Maximum number of files reached"
  },
  // ProductPartnerController
  {
    "Localized_Key": "ITEMS_NOT_FOUND",
    "Localized_Value": "Items not found"
  },
  {
    "Localized_Key": "PRODUCT_NOT_FOUND",
    "Localized_Value": "Product not found"
  },
  {
    "Localized_Key": "PRODUCT_NOT_YOURS",
    "Localized_Value": "This product is not yours"
  },
  {
    "Localized_Key": "PRODUCT_DELETE_SUCCESS",
    "Localized_Value": "Product deleted successfully"
  },
  {
    "Localized_Key": "PRODUCT_CATEGORY_INVALID",
    "Localized_Value": "Invalid category"
  },
  {
    "Localized_Key": "PRODUCT_SUB_CATEGORY_INVALID",
    "Localized_Value": "Invalid subcategory"
  },
  {
    "Localized_Key": "PRODUCT_NAME_INVALID",
    "Localized_Value": "Invalid product name"
  },
  {
    "Localized_Key": "PRODUCT_IMAGES_INVALID",
    "Localized_Value": "Invalid product image"
  },
  {
    "Localized_Key": "PRODUCT_IMAGES_LINK_INVALID",
    "Localized_Value": "Invalid product image link"
  },
  {
    "Localized_Key": "PRODUCT_PRICE_LARGER_CAPITAL",
    "Localized_Value": "Product price cannot be less than capital price"
  },
  {
    "Localized_Key": "PRODUCT_REAL_LARGER_CAPITAL",
    "Localized_Value": "Product price cannot be less than capital price"
  },
  {
    "Localized_Key": "PRODUCT_PRICE_LARGER_REAL",
    "Localized_Value": "Product price cannot be greater than real price"
  },
  {
    "Localized_Key": "PRODUCT_WAREHOUSE_INVALID",
    "Localized_Value": "Invalid warehouse location"
  },
  {
    "Localized_Key": "PRODUCT_LIST_VARIANT_INVALID",
    "Localized_Value": "Invalid variant list"
  },
  {
    "Localized_Key": "PRODUCT_VARIANT_INVALID",
    "Localized_Value": "Invalid variant"
  },
  {
    "Localized_Key": "PRODUCT_VARIANT_ID_INVALID",
    "Localized_Value": "Invalid variant items id"
  },
  {
    "Localized_Key": "SERVICE_NOT_FOUND",
    "Localized_Value": "Service not found"
  },
  {
    "Localized_Key": "SERVICE_NOT_YOURS",
    "Localized_Value": "This service is not yours"
  },
  {
    "Localized_Key": "SERVICE_DELETE_SUCCESS",
    "Localized_Value": "Service Deleted Successfully"
  },
  {
    "Localized_Key": "SERVICE_CATEGORY_INVALID",
    "Localized_Value": "Invalid Category"
  },
  {
    "Localized_Key": "SERVICE_SUB_CATEGORY_INVALID",
    "Localized_Value": "Invalid Subcategory"
  },
  {
    "Localized_Key": "SERVICE_NAME_INVALID",
    "Localized_Value": "Invalid Service Name"
  },
  {
    "Localized_Key": "SERVICE_IMAGES_INVALID",
    "Localized_Value": "Invalid Service Image"
  },
  {
    "Localized_Key": "SERVICE_IMAGES_LINK_INVALID",
    "Localized_Value": "Invalid Service Image Link"
  },
  {
    "Localized_Key": "SERVICE_PRICE_LARGER_CAPITAL",
    "Localized_Value": "Service price cannot be less than capital price"
  },
  {
    "Localized_Key": "SERVICE_REAL_LARGER_CAPITAL",
    "Localized_Value": "Service price cannot be less than capital price"
  },
  {
    "Localized_Key": "SERVICE_PRICE_LARGER_REAL",
    "Localized_Value": "Service price cannot be greater than real price"
  },
  {
    "Localized_Key": "PRODUCT_LIMIT_ITEMS",
    "Localized_Value": "The number of products has exceeded the maximum limit"
  },
  {
    "Localized_Key": "SERVICE_LIMIT_ITEMS",
    "Localized_Value": "The number of services has exceeded the maximum limit"
  },
  // VoucherPartnerController
  {
    "Localized_Key": "VOUCHER_NOT_FOUND",
    "Localized_Value": "Voucher not found"
  },
  {
    "Localized_Key": "VOUCHER_NOT_YOURS",
    "Localized_Value": "This voucher is not yours"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_SUCCESS",
    "Localized_Value": "Voucher deleted successfully"
  },
  {
    "Localized_Key": "VOUCHERS_DELETE_SUCCESS",
    "Localized_Value": "Vouchers deleted successfully"
  },
  {
    "Localized_Key": "VOUCHER_UNACTIVE_SUCCESS",
    "Localized_Value": "Voucher disabled successfully"
  },
  {
    "Localized_Key": "VOUCHER_CHANGE_ACTIVE_SUCCESS",
    "Localized_Value": "Voucher change active successfully"
  },
  {
    "Localized_Key": "VOUCHER_TYPE_INVALID",
    "Localized_Value": "Voucher type is invalid"
  },
  {
    "Localized_Key": "VOUCHER_EXCHANGE_POINTS_INVALID",
    "Localized_Value": "Invalid exchange points"
  },
  {
    "Localized_Key": "VOUCHER_QUANTITY_INVALID",
    "Localized_Value": "Invalid voucher quantity"
  },
  {
    "Localized_Key": "VOUCHER_NUM_USE_INVALID",
    "Localized_Value": "Number of uses allowed is invalid"
  },
  {
    "Localized_Key": "VOUCHER_PERCENT_DISCOUNT_INVALID",
    "Localized_Value": "Invalid discount percentage (must be 0-100%)"
  },
  {
    "Localized_Key": "VOUCHER_MIN_ORDER_INVALID",
    "Localized_Value": "Invalid min order value"
  },
  {
    "Localized_Key": "VOUCHER_MAX_DISCOUNT_INVALID",
    "Localized_Value": "Invalid max discount value"
  },
  {
    "Localized_Key": "VOUCHER_LIMIT_TYPE_FOR_PROMOTION_ONLY",
    "Localized_Value": "This limit type is only for promotion vouchers"
  },
  {
    "Localized_Key": "VOUCHER_LIMIT_TYPE_FOR_TRANSPORT_ONLY",
    "Localized_Value": "This limit type is only for transport vouchers"
  },
  {
    "Localized_Key": "VOUCHER_CATEGORY_INVALID",
    "Localized_Value": "Invalid category id"
  },
  {
    "Localized_Key": "VOUCHER_CATEGORY_NOT_FOUND",
    "Localized_Value": "Category does not exist in the system"
  },
  {
    "Localized_Key": "VOUCHER_PRODUCT_IDS_INVALID",
    "Localized_Value": "Invalid product ids list"
  },
  {
    "Localized_Key": "VOUCHER_TRANSPORT_MIN_MONEY_INVALID",
    "Localized_Value": "Min money for shipping voucher is invalid"
  },
  {
    "Localized_Key": "VOUCHER_PRODUCT_IDS_INVALID",
    "Localized_Value": "Invalid item ids list"
  },
  {
    "Localized_Key": "VOUCHER_GROUP_NAME_INVALID",
    "Localized_Value": "Invalid user group name"
  },
  {
    "Localized_Key": "VOUCHER_GROUP_NAME_NOT_FOUND",
    "Localized_Value": "User group name does not exist"
  },
  {
    "Localized_Key": "VOUCHER_USERS_IDS_INVALID",
    "Localized_Value": "Invalid user ids list"
  },
  {
    "Localized_Key": "VOUCHER_START_END_DATE_INVALID",
    "Localized_Value": "Invalid start and end dates"
  },
  {
    "Localized_Key": "VOUCHER_END_AFTER_START_DATE_INVALID",
    "Localized_Value": "End date must be after start date"
  },
  {
    "Localized_Key": "VOUCHER_SEARCH_USER_INVALID",
    "Localized_Value": "Please select a customer to search for matching vouchers"
  },
  {
    "Localized_Key": "VOUCHER_IS_EXCHANGED",
    "Localized_Value": "Voucher is exchanged"
  },
  {
    "Localized_Key": "QUANTITY_VOUCHER_IS_NOT_VALID",
    "Localized_Value": "Quantity voucher is not valid"
  },
  {
    "Localized_Key": "USER_POINT_IS_NOT_VALID",
    "Localized_Value": "User point is not valid"
  },
  {
    "Localized_Key": "VOUCHER_IS_NOT_BELONG_SHOP",
    "Localized_Value": "Voucher is not belong this shop"
  },

  // User Address
  {
    "Localized_Key": "USER_NOT_EXIST",
    "Localized_Value": "Người dùng không tồn tại"
  },
  {
    "Localized_Key": "USER_NOT_PARTNER_MEMBER",
    "Localized_Value": "User not a partner member"
  },
  {
    "Localized_Key": "ERROR_CANNOT_DELETE_DEFAULT_ADDRESS",
    "Localized_Value": "Cannot delete default address"
  },
  {
    "Localized_Key": "ERROR_ADDRESS_NOT_FOUND",
    "Localized_Value": "Address not found"
  },
  {
    "Localized_Key": "ERROR_CANNOT_SET_DEFAULT_ADDRESS",
    "Localized_Value": "Cannot set default address"
  },
  {
    "Localized_Key": "ERROR_MUST_HAVE_ONE_DEFAULT_ADDRESS",
    "Localized_Value": "Must have at least one default address"
  },
  {
    "Localized_Key": "ERROR_CANNOT_DEACTIVATE_ADDRESS",
    "Localized_Value": "Cannot deactivate this address"
  },
  {
    "Localized_Key": "SUCCESS_ADDRESS_DELETED",
    "Localized_Value": "Success delete address"
  },

  // OrderPartnerController
  {
    "Localized_Key": "CART_NOT_FOUND",
    "Localized_Value": "Cart not found"
  },
  {
    "Localized_Key": "CART_DELETE_SUCCESS",
    "Localized_Value": "Cart delete success"
  },
  {
    "Localized_Key": "ORDER_NOT_PAID",
    "Localized_Value": "Order not paid"
  },
  {
    "Localized_Key": "ORDER_PAID_OR_REFUNDED",
    "Localized_Value": "Order is paid or refunded"
  },
  {
    "Localized_Key": "CART_NOT_YOURS",
    "Localized_Value": "This cart is not yours"
  },
  {
    "Localized_Key": "CART_USER_NOT_FOUND",
    "Localized_Value": "User not found"
  },
  {
    "Localized_Key": "CART_ADDRESS_NOT_FOUND",
    "Localized_Value": "Invalid shipping address"
  },
  {
    "Localized_Key": "CART_LIST_ITEMS_INVALID",
    "Localized_Value": "Invalid item list"
  },
  {
    "Localized_Key": "CART_LIST_ITEMS_PRICE_INVALID",
    "Localized_Value": "Invalid item price"
  },
  {
    "Localized_Key": "CART_LIST_ITEMS_QUANTITY_PURCHASE_INVALID",
    "Localized_Value": "Invalid item purchase quantity"
  },
  {
    "Localized_Key": "CART_LIST_VOUCHER_PROMOTION_INVALID",
    "Localized_Value": "Promotion voucher list invalid"
  },
  {
    "Localized_Key": "CART_LIST_VOUCHER_TRANSPORT_INVALID",
    "Localized_Value": "Transport voucher list invalid"
  },
  {
    "Localized_Key": "CART_LIST_VOUCHER_PROMOTION_UNAVAILABLE",
    "Localized_Value": "List of promotional vouchers that are no longer available to customers"
  },
  {
    "Localized_Key": "CART_LIST_VOUCHER_TRANSPORT_UNAVAILABLE",
    "Localized_Value": "List of shipping vouchers that are no longer available to customers"
  },
  {
    "Localized_Key": "ORDER_PARTNER_NOTFOUND",
    "Localized_Value": "No partner found for order"
  },
  {
    "Localized_Key": "ORDER_LIST_ITEMS_NOTEXITS",
    "Localized_Value": "No products found in cart"
  },
  {
    "Localized_Key": "ORDER_ITEMS_NOTFOUND_QUALITY_EMPTY",
    "Localized_Value": "Product does not exist or quantity purchased is incorrect"
  },
  {
    "Localized_Key": "ORDER_ITEMS_NAME_INVALID",
    "Localized_Value": "Invalid product name"
  },
  {
    "Localized_Key": "ORDER_ITEMS_PRICE_INVALID",
    "Localized_Value": "Invalid product price"
  },
  {
    "Localized_Key": "ORDER_ITEMS_NOTFOUND",
    "Localized_Value": "Product not found"
  },
  {
    "Localized_Key": "ORDER_PRODUCT_NAME",
    "Localized_Value": "Product"
  },
  {
    "Localized_Key": "ORDER_PRODUCT_NOT_EXITS_OR_QUANTITY_EMPTY",
    "Localized_Value": "does not exist or quantity is not enough to supply"
  },
  {
    "Localized_Key": "ORDER_PRODUCT_SOLD_OUT",
    "Localized_Value": "out of stock"
  },
  {
    "Localized_Key": "ORDER_PRICE_ROWNG",
    "Localized_Value": "wrong price"
  },
  {
    "Localized_Key": "ORDER_PROMOTION_NAME",
    "Localized_Value": "Promotio voucher"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_NOTFOUND",
    "Localized_Value": "Voucher not found"
  },
  {
    "Localized_Key": "ORDER_PROMOTION_FROM_DATE",
    "Localized_Value": "only applicable from date"
  },
  {
    "Localized_Key": "ORDER_PROMOTION_TO_DATE",
    "Localized_Value": "until date"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_CANNOT_USE",
    "Localized_Value": "Voucher cannot be used with product list"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_MINORDER",
    "Localized_Value": "Voucher cannot be used. Voucher requires minimum purchase amount"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_USED_OUT",
    "Localized_Value": "Voucher used up all times"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_USER_USED_OUT",
    "Localized_Value": "The voucher has been used up to the allowed number of times by the customer"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_TRANSPORT_NAME",
    "Localized_Value": "Transport voucher"
  },
  {
    "Localized_Key": "ORDER_SHOP_NOT_EXITS",
    "Localized_Value": "Product not found shop"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_TRANSPORT_TYPE_INVALID",
    "Localized_Value": "List of invalid shipping vouchers"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_PROMOTION_TYPE_INVALID",
    "Localized_Value": "List of invalid promotion vouchers"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_EXCHANGE_POINTS_INVALID",
    "Localized_Value": "Voucher does not have enough points to redeem"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_CATEGORY_INVALID",
    "Localized_Value": "Voucher applied to a category that does not match the product list"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_ITEMS_INVALID",
    "Localized_Value": "Voucher assigned to a product that does not match the product list"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_GROUP_NAME_INVALID",
    "Localized_Value": "Voucher applied to a customer group that does not match the product list"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_CUSTOMER_INVALID",
    "Localized_Value": "Voucher assigned to incorrect customer"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_PROMOTION_EXPIRED",
    "Localized_Value": "The promotion voucher has expired"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_TRANSPORT_EXPIRED",
    "Localized_Value": "The transport voucher has expired"
  },
  {
    "Localized_Key": "ORDER_NOTFOUND",
    "Localized_Value": "Order not found"
  },
  {
    "Localized_Key": "ORDER_PAY_NOT_MATCH",
    "Localized_Value": "This order was not paid for using the selected payment method"
  },
  {
    "Localized_Key": "ORDER_AMOUNT_NOT_MATCH",
    "Localized_Value": "Invalid amount"
  },
  {
    "Localized_Key": "ORDER_IS_DONE",
    "Localized_Value": "Order completed or does not exist"
  },
  {
    "Localized_Key": "ORDER_IS_BEING_DELIVERED",
    "Localized_Value": "Order is being delivered"
  },
  {
    "Localized_Key": "ORDER_IS_REFUND",
    "Localized_Value": "Order is refunded"
  },
  {
    "Localized_Key": "ORDER_IS_FAILED",
    "Localized_Value": "Order cancelled"
  },
  {
    "Localized_Key": "ORDER_TYPE_PAY_INVALID",
    "Localized_Value": "Payment method not available"
  },
  {
    "Localized_Key": "ORDER_AMOUNT_NOT_MATH",
    "Localized_Value": "Total payment amount is incorrect or product price has changed"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_PROMOTION_NOT_MATH",
    "Localized_Value": "Total promotion voucher amount is incorrect or has changed"
  },
  {
    "Localized_Key": "ORDER_VOUCHER_TRANSPORT_NOT_MATH",
    "Localized_Value": "Total shipping voucher amount is incorrect or has changed"
  },
  {
    "Localized_Key": "ORDER_TRANSPORT_NOT_MATH",
    "Localized_Value": "Total shipping fee is incorrect or fee has changed"
  },
  {
    "Localized_Key": "ORDER_AMOUNT_REJECT",
    "Localized_Value": "Request rejected because transaction amount is less than 1,000 VND or greater than 50,000,000 VND"
  },
  {
    "Localized_Key": "ORDER_POINT_INVALID",
    "Localized_Value": "Invalid exchange points"
  },
  {
    "Localized_Key": "ORDER_PRODUCT_EXCEED_MAX_PURCHASE_LIMIT",
    "Localized_Value": "The ordered quantity exceeds the maximum purchase limit"
  },
  //SettingSystemPartner
  {
    "Localized_Key": "DOMAIN_NOT_FOUND",
    "Localized_Value": "Domain not found"
  },
  {
    "Localized_Key": "DOMAIN_EXISTED",
    "Localized_Value": "Domain existed"
  },
  {
    "Localized_Key": "DOMAIN_DELETED_SUCCESS",
    "Localized_Value": "Domain deleted successfully"
  },
  {
    "Localized_Key": "SHOP_DOMAIN_EXISTED",
    "Localized_Value": "Shop domain's already exists"
  },
  //OrderUser
  {
    "Localized_Key": "ORDER_INVALID",
    "Localized_Value": "Invalid order"
  },
  {
    "Localized_Key": "ORDER_TRANSPORT_SERVICE_INVALID",
    "Localized_Value": "Transaction service invalid"
  },
  //UserPartner
  {
    "Localized_Key": "REFERRER_CODE_INVALID",
    "Localized_Value": "Referrer code invalid"
  },
  //SHOP_SETTING
  {
    "Localized_Key": "SHOP_SETTING_NOT_FOUND",
    "Localized_Value": "Shop setting not found"
  },
  //PAYMENT
  {
    "Localized_Key": "PAYMENT_NOT_FOUND",
    "Localized_Value": "Payment not found"
  },
  {
    "Localized_Key": "PAYMENT_DELETE_SUCCESS",
    "Localized_Value": "Payment delete success"
  },
  //MEMBERSHIP_LEVEL
  {
    "Localized_Key": "MEMBERSHIPLEVEL_DELETE_SUCCESS",
    "Localized_Value": "Membershiplevel deleted successfully"
  },
  {
    "Localized_Key": "MEMBERSHIPLEVEL_NOT_FOUND",
    "Localized_Value": "MembershipLevel not found"
  },
  {
    "Localized_Key": "INVALID_POINT_RATE",
    "Localized_Value": "Point rate must be a multiple of 5 and between 0-100. Please enter a valid value."
  },
  {
    "Localized_Key": "SPENDING_THRESHOLD_INVALID",
    "Localized_Value": "Spending threshold is invalid. Please enter a valid value."
  },
  {
    "Localized_Key": "POINT_MUST_BE_POSITIVE_FOR_ADD",
    "Localized_Value": "Points must be positive when adding points."
  },
  {
    "Localized_Key": "POINT_MUST_BE_NEGATIVE_FOR_DEDUCT",
    "Localized_Value": "Points must be negative when deducting points."
  },
  {
    "Localized_Key": "INVALID_TO_DATE",
    "Localized_Value": "Invalid end date. Please select a valid date."
  },
  {
    "Localized_Key": "MEMBERSHIPCONFIG_NOT_FOUND",
    "Localized_Value": "Membership configuration not found."
  },
  {
    "Localized_Key": "INVALID_EARN_POINT_FORMAT",
    "Localized_Value": "Invalid earn point format."
  },
  {
    "Localized_Key": "REGISTER_RULE_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Registration rule must be greater than 0."
  },
  {
    "Localized_Key": "SHARE_RULE_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Share rule must be greater than 0."
  },
  {
    "Localized_Key": "SHARE_MAX_TURNS_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Maximum share turns must be greater than 0."
  },
  {
    "Localized_Key": "PURCHASE_MIN_SPENT_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Minimum purchase amount must be greater than 0."
  },
  {
    "Localized_Key": "INVALID_EXCHANGE_POINTS_FORMAT",
    "Localized_Value": "Invalid exchange points format."
  },
  {
    "Localized_Key": "DISCOUNT_RULE_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Discount rule must be greater than 0."
  },
  {
    "Localized_Key": "IS_SCORE_VALUE_MUST_BE_GREATER_THAN_ZERO",
    "Localized_Value": "Score value must be greater than 0."
  },
  {
    "Localized_Key": "UPDATE_POINTS_SUCCESS",
    "Localized_Value": "Points updated successfully!"
  },
  {
    "Localized_Key": "UPDATE_POINTS_FAIL_INSUFFICIENT",
    "Localized_Value": "The deducted points exceed the available balance!"
  },
  //CONFIG_MEMBERSHIP_LEVEL
  {
    "Localized_Key": "VOUCHER_EXCHANGE_DISABLED",
    "Localized_Value": "Voucher exchange disabled"
  },
  {
    "Localized_Key": "CONFIG_MEMBERSHIPLEVEL_NOT_FOUND",
    "Localized_Value": "Membership level configuration not found"
  },
  {
    "Localized_Key": "CONFIG_MEMBERSHIPLEVEL_DISABLED",
    "Localized_Value": "Membership program status disabled"
  },
  //PARTNER
  {
    "Localized_Key": "PARTNER_INVALID_ID",
    "Localized_Value": "Partner not found"
  },
  //PARTNER OA
  {
    "Localized_Key": "PARTNER_OA_INVALID_ID",
    "Localized_Value": "Invalid partner OA ID"
  },
  {
    "Localized_Key": "PARTNER_NOT_FOUND",
    "Localized_Value": "Partner not found"
  },
  {
    "Localized_Key": "PARTNER_OA_NOT_FOUND",
    "Localized_Value": "Partner OA not found"
  },
  {
    "Localized_Key": "PARTNER_OA_CREATE_SUCCESS",
    "Localized_Value": "Partner OA created successfully"
  },
  {
    "Localized_Key": "PARTNER_OA_UPDATE_SUCCESS",
    "Localized_Value": "Partner OA updated successfully"
  },
  {
    "Localized_Key": "PARTNER_OA_DELETE_SUCCESS",
    "Localized_Value": "Partner OA deleted successfully"
  },
  {
    "Localized_Key": "PARTNER_OA_ALREADY_EXISTS",
    "Localized_Value": "Partner OA already exists"
  },
  //Zalo OA
  {
    "Localized_Key": "ZALO_OA_AUTH_URL_SUCCESS",
    "Localized_Value": "Successfully retrieved Zalo OA authorization URL"
  },
  {
    "Localized_Key": "ZALO_OA_AUTH_SUCCESS",
    "Localized_Value": "Zalo OA authorization successful"
  },
  {
    "Localized_Key": "ZALO_OA_AUTH_ALREADY",
    "Localized_Value": "Zalo OA authorization has already been completed or is not active"
  },
  {
    "Localized_Key": "ZALO_OA_UNAUTHORIZED",
    "Localized_Value": "OA is not authorized. Please authorize the Zalo Official Account before using this feature."
  },
  {
    "Localized_Key": "ZALO_TEMPLATEID_REQUIRED",
    "Localized_Value": "TemplateId must not be empty"
  },
  {
    "Localized_Key": "ZALO_BUTTON_URL_EMPTY",
    "Localized_Value": "CTA URL is empty"
  },
  {
    "Localized_Key": "ZALO_BUTTON_URL_INVALID",
    "Localized_Value": "CTA URL is invalid"
  },
  {
    "Localized_Key": "ZALO_BUTTON_PHONE_EMPTY",
    "Localized_Value": "CTA phone number is empty"
  },
  {
    "Localized_Key": "ZALO_BUTTON_PHONE_INVALID",
    "Localized_Value": "CTA phone number is invalid"
  },
  //BASE
  {
    "Localized_Key": "INTERNAL_SERVER_ERROR",
    "Localized_Value": "Internal server error"
  },
  {
    "Localized_Key": "BAD_REQUEST",
    "Localized_Value": "Bad request"
  },
  {
    "Localized_Key": "NOT_FOUND",
    "Localized_Value": "Resource not found"
  },
  {
    "Localized_Key": "METHOD_NOT_ALLOWED",
    "Localized_Value": "Method not allowed"
  },
  {
    "Localized_Key": "REQUEST_TIMEOUT",
    "Localized_Value": "Request timeout"
  },
  {
    "Localized_Key": "SERVICE_UNAVAILABLE",
    "Localized_Value": "Service unavailable"
  },
  {
    "Localized_Key": "UNKNOWN_ERROR",
    "Localized_Value": "Unknown error"
  },
  {
    "Localized_Key": "UNAUTHORIZED",
    "Localized_Value": "Unauthorized"
  },
  {
    "Localized_Key": "FORBIDDEN",
    "Localized_Value": "Forbidden"
  },
  {
    "Localized_Key": "INVALID_CREDENTIALS",
    "Localized_Value": "Invalid credentials"
  },
  {
    "Localized_Key": "ACCOUNT_LOCKED",
    "Localized_Value": "Account locked"
  },
  {
    "Localized_Key": "TOKEN_EXPIRED",
    "Localized_Value": "Token expired"
  },
  {
    "Localized_Key": "TOKEN_INVALID",
    "Localized_Value": "Invalid token"
  },
  {
    "Localized_Key": "ACCESS_DENIED",
    "Localized_Value": "Access denied"
  },
  {
    "Localized_Key": "DATA_NOT_FOUND",
    "Localized_Value": "No data found"
  },
  {
    "Localized_Key": "JOB_NOT_FOUND",
    "Localized_Value": "Job not found"
  },
  {
    "Localized_Key": "DUPLICATE_ENTRY",
    "Localized_Value": "Duplicate entry"
  },
  {
    "Localized_Key": "VALIDATION_FAILED",
    "Localized_Value": "Validation failed"
  },
  {
    "Localized_Key": "DATA_CONFLICT",
    "Localized_Value": "Data conflict"
  },
  {
    "Localized_Key": "DATABASE_ERROR",
    "Localized_Value": "Database error"
  },
  {
    "Localized_Key": "EXTERNAL_SERVICE_ERROR",
    "Localized_Value": "External service error"
  },
  {
    "Localized_Key": "RATE_LIMIT_EXCEEDED",
    "Localized_Value": "Rate limit exceeded"
  },
  {
    "Localized_Key": "SUCCESS",
    "Localized_Value": "Success"
  },
  {
    "Localized_Key": "FAIL",
    "Localized_Value": "Failure"
  },
  {
    "Localized_Key": "CREATED_SUCCESSFULLY",
    "Localized_Value": "Created successfully"
  },
  {
    "Localized_Key": "UPDATED_SUCCESSFULLY",
    "Localized_Value": "Updated successfully"
  },
  {
    "Localized_Key": "DELETED_SUCCESSFULLY",
    "Localized_Value": "Deleted successfully"
  },
  {
    "Localized_Key": "NO_CONTENT",
    "Localized_Value": "No content"
  },
  {
    "Localized_Key": "IMPORT_SUCCESSFULLY",
    "Localized_Value": "Import successfully"
  },

  // SYNC SERVICE
  {
    "Localized_Key": "SYNC_SERVICE_NOT_SUPPORTED",
    "Localized_Value": "Sync service not supported"
  },
  {
    "Localized_Key": "SYNC_NHANH_CONFIG_SAVE_ERROR",
    "Localized_Value": "Error saving Nhanh.vn configuration"
  },
  {
    "Localized_Key": "SYNC_NHANH_CONFIG_NOT_FOUND",
    "Localized_Value": "Nhanh.vn configuration not found"
  },
  {
    "Localized_Key": "SYNC_NHANH_CONFIG_DELETE_ERROR",
    "Localized_Value": "Error deleting Nhanh.vn configuration"
  },
  {
    "Localized_Key": "SYNC_NHANH_ACCESS_CODE_UPDATE_ERROR",
    "Localized_Value": "Error updating Nhanh.vn access code"
  },
  {
    "Localized_Key": "SYNC_NHANH_PRODUCT_SYNC_ERROR",
    "Localized_Value": "Error syncing Nhanh.vn product"
  },
  {
    "Localized_Key": "SYNC_NHANH_ORDER_SYNC_ERROR",
    "Localized_Value": "Error syncing Nhanh.vn order"
  },
  {
    "Localized_Key": "SYNC_NHANH_CUSTOMER_SYNC_ERROR",
    "Localized_Value": "Error syncing Nhanh.vn customer"
  },
  {
    "Localized_Key": "SYNC_NHANH_PRODUCT_DELETE_ERROR",
    "Localized_Value": "Error deleting Nhanh.vn products"
  },
  {
    "Localized_Key": "SYNC_NHANH_ORDER_DELETE_ERROR",
    "Localized_Value": "Error deleting Nhanh.vn order"
  },
  {
    "Localized_Key": "SYNC_NHANH_ORDER_UPDATE_ERROR",
    "Localized_Value": "Error updating order to Nhanh.vn"
  },
  {
    "Localized_Key": "SYNC_NHANH_CATEGORY_NOT_FOUND",
    "Localized_Value": "Category not found in Nhanh.vn"
  },
  {
    "Localized_Key": "SYNC_NHANH_SHOP_NOT_CONFIGURED",
    "Localized_Value": "Shop not configured for Nhanh.vn"
  },
  {
    "Localized_Key": "SYNC_NHANH_ORDER_ID_NOT_FOUND",
    "Localized_Value": "Nhanh.vn order ID or code not found for update"
  },
  {
    "Localized_Key": "SYNC_NHANH_UPDATE_ORDER_FAILED",
    "Localized_Value": "Failed to update order to Nhanh.vn"
  },
  {
    "Localized_Key": "SYNC_NHANH_API_ERROR",
    "Localized_Value": "Nhanh.vn API returned error"
  },
  // AFFILIATION
  {
    "Localized_Key": "COMMISSIONS_CONFIG_NOT_ACTIVE",
    "Localized_Value": "Commissions config not active"
  },
  {
    "Localized_Key": "MULTIPLE_COMMISSIONS_CONFIGS_FOUND",
    "Localized_Value": "Multiple commissions config found"
  },
  {
    "Localized_Key": "COMMISSIONS_CONFIGS_EXIST",
    "Localized_Value": "Shop already have a commissions config"
  },
  {
    "Localized_Key": "NOT_FOUND_COMMISSIONS_CONFIG",
    "Localized_Value": "Not found Commisisons config"
  },
  {
    "Localized_Key": "DATE_RANGE_VALID",
    "Localized_Value": "From date must be less than or equal to to date"
  },
  {
    "Localized_Key": "PARTNER_NOT_FOUND",
    "Localized_Value": "Partner not found"
  },
  {
    "Localized_Key": "COMMISSIONS_CONFIGS_EXIST",
    "Localized_Value": "Shop already have a commissions config"
  },
  {
    "Localized_Key": "USERID_DUPLICATED_COMMISSIONS_CONFIG",
    "Localized_Value": "UserId is duplicated in other configs"
  },
  {
    "Localized_Key": "ITEMSCODE_DUPLICATED_COMMISSIONS_CONFIG",
    "Localized_Value": "Itemscode is duplicated in other item group"
  },
  {
    "Localized_Key": "RECRUITMENT_PAGE_NOT_FOUND",
    "Localized_Value": "Recruitment Page not found"
  },
  {
    "Localized_Key": "FILE_UPLOAD_FAILED",
    "Localized_Value": "File upload failed"
  },
  {
    "Localized_Key": "PARTNER_INSUFFICIENT_SPENDING",
    "Localized_Value": "The total spending is insufficient to qualify for business partnership"
  },
  {
    "Localized_Key": "COMMISSION_CONFIG_NOT_ACTIVE",
    "Localized_Value": "Commission config not active"
  },
  {
    "Localized_Key": "REFERRER_CODE_INVALID_CYCLE",
    "Localized_Value": "Invalid referrer code due to a cycle"
  },
  //Zalo_BaoCao
  {
    "Localized_Key": "START_DATE_MUST_BE_BEFORE_OR_EQUAL_END_DATE",
    "Localized_Value": "Start date must be before or equal to end date"
  },
  //Export
  {
    "Localized_Key": "INVALID_TEMPLATE_FORMAT",
    "Localized_Value": "Invalid template format, please use the sample template"
  },
  {
    "Localized_Key": "HAVE_ORDER_NOT_CAN_EXPORT",
    "Localized_Value": "Self-delivery shop orders cannot issue bills of lading"
  },
  {
    "Localized_Key": "FILE_CONTAINS_NO_VALID_DATA",
    "Localized_Value": "The file contains no valid data due to duplicate entries or incorrect formatting"
  },
  {
    "Localized_Key": "ROW",
    "Localized_Value": "Row"
  },
  {
    "Localized_Key": "ROW_REQUIRED",
    "Localized_Value": "cannot be empty"
  },
  {
    "Localized_Key": "MUST_BE_NUMBER",
    "Localized_Value": "must be number"
  },
  {
    "Localized_Key": "ROW_INVALID",
    "Localized_Value": "invalid"
  },
  {
    "Localized_Key": "ROW_EXISTED",
    "Localized_Value": "already exists"
  },
  {
    "Localized_Key": "FULL_NAME",
    "Localized_Value": "Full name"
  },
  {
    "Localized_Key": "PHONE_NUMBER",
    "Localized_Value": "Phone number"
  },
  {
    "Localized_Key": "CATEGORY_NAME",
    "Localized_Value": "Category name"
  },
  {
    "Localized_Key": "CATEGORY",
    "Localized_Value": "Category"
  },
  {
    "Localized_Key": "ITEMS_NAME",
    "Localized_Value": "Product/Service Name"
  },
  {
    "Localized_Key": "PRICE",
    "Localized_Value": "Selling Price"
  },
  {
    "Localized_Key": "PRICE_REAL",
    "Localized_Value": "Original Price"
  },
  {
    "Localized_Key": "PRICE_CAPITAL",
    "Localized_Value": "Cost Price"
  },
  {
    "Localized_Key": "QUANTITY",
    "Localized_Value": "Quantity"
  },
  {
    "Localized_Key": "IMAGES",
    "Localized_Value": "Images"
  },
  {
    "Localized_Key": "ITEMS_INFO",
    "Localized_Value": "Product/Service Description"
  },
  {
    "Localized_Key": "QUANTITY_SOLD",
    "Localized_Value": "Quantity Sold"
  },
  {
    "Localized_Key": "ITEMS_POSITION",
    "Localized_Value": "Items Position"
  },
  {
    "Localized_Key": "INSUFFICIENT_BALANCE",
    "Localized_Value": "Insufficient balance"
  },
  //Invoice
  {
    "Localized_Key": "CONFIG_CREDENTIALS",
    "Localized_Value": "Incorrect electronic invoice login information"
  },
  {
    "Localized_Key": "TAX_CODE_REQUIRED",
    "Localized_Value": "Tax code is required"
  },
  {
    "Localized_Key": "NAME_REQUIRED",
    "Localized_Value": "Company or individual name is required"
  },
  {
    "Localized_Key": "INVOICE_ISSUED_SUCCESSFULLY",
    "Localized_Value": "Invoice has been issued successfully"
  },
  {
    "Localized_Key": "INVOICE_ISSUE_FAILED",
    "Localized_Value": "Failed to issue the invoice. Please check the information and try again"
  },
  //Campaign
  {
    "Localized_Key": "CAMPAIGN_RUNNING_UPDATE_FORBIDDEN",
    "Localized_Value": "Cannot update while the campaign is running"
  },
  {
    "Localized_Key": "SCHEDULE_TIME_INVALID",
    "Localized_Value": "The scheduled time is invalid"
  },
  {
    "Localized_Key": "ROW_MIN_LENGTH",
    "Localized_Value": "the minimum row length is invalid"
  },
  {
    "Localized_Key": "ROW_MAX_LENGTH",
    "Localized_Value": "the maximum row length is invalid"
  },
  {
    "Localized_Key": "ROW_INVALID_STRING",
    "Localized_Value": "contains invalid characters"
  },
  {
    "Localized_Key": "ROW_INVALID_NUMBER",
    "Localized_Value": "is not a valid number"
  },
  {
    "Localized_Key": "ROW_INVALID_CURRENCY",
    "Localized_Value": "is not a valid currency format"
  },
  {
    "Localized_Key": "ROW_INVALID_BANK_NOTE",
    "Localized_Value": "contains unauthorized characters in the transfer note"
  },
  //Export
  {
    "Localized_Key": "PURCHASE_SUCCESSFULLY",
    "Localized_Value": "Purchase successful"
  },
  {
    "Localized_Key": "PACKAGE_ACTIVATED",
    "Localized_Value": "Package activated"
  },
  // Partner role
  {
    "Localized_Key": "ROLE_NOT_FOUND",
    "Localized_Value": "Role not found"
  },
  {
    "Localized_Key": "ROLE_CREATED_SUCCESSFULLY",
    "Localized_Value": "Role created successfully"
  },
  {
    "Localized_Key": "INVALID_PERMISSIONS",
    "Localized_Value": "Invalid permissions"
  },
  {
    "Localized_Key": "ROLE_UPDATED_SUCCESSFULLY",
    "Localized_Value": "Role updated successfully"
  },
  {
    "Localized_Key": "ROLE_IN_USE_BY_PARTNER",
    "Localized_Value": "Role is in use by a partner"
  },
  {
    "Localized_Key": "ROLE_DELETED_SUCCESSFULLY",
    "Localized_Value": "Role deleted successfully"
  },
  {
    "Localized_Key": "ROLE_NAME_ALREADY_EXISTS",
    "Localized_Value": "A role with this name already exists"
  },
  // Partner package
  {
    "Localized_Key": "PACKAGE_ACTIVATED",
    "Localized_Value": "Package activated successfully"
  },
  {
    "Localized_Key": "PACKAGE_NOT_FOUND",
    "Localized_Value": "Package not found"
  },
  {
    "Localized_Key": "PURCHASE_SUCCESSFULLY",
    "Localized_Value": "Package purchased successfully"
  },
  {
    "Localized_Key": "NO_ACTIVE_PACKAGES",
    "Localized_Value": "No active packages"
  },
  {
    "Localized_Key": "NO_PACKAGES_AVAILABLE",
    "Localized_Value": "No packages available"
  },
  {
    "Localized_Key": "PARTNER_NOT_FOUND",
    "Localized_Value": "Partner not found"
  },
  {
    "Localized_Key": "OLD_PACKAGE_NOT_FOUND",
    "Localized_Value": "No package has been activated"
  },
  {
    "Localized_Key": "PACKAGE_DOWNGRADE_NOT_ALLOWED",
    "Localized_Value": "Package downgrade is not allowed"
  },
  // Employee
  {
    "Localized_Key": "EMPLOYEE_CREATED_SUCCESSFULLY",
    "Localized_Value": "Employee created successfully"
  },
  {
    "Localized_Key": "EMPLOYEE_NOT_FOUND",
    "Localized_Value": "Employee not found"
  },
  {
    "Localized_Key": "EMPLOYEE_UPDATED_SUCCESSFULLY",
    "Localized_Value": "Employee updated successfully"
  },
  {
    "Localized_Key": "EMPLOYEE_DELETED_SUCCESSFULLY",
    "Localized_Value": "Employee deleted successfully"
  },
  {
    "Localized_Key": "AUTH_PHONE_REGISTERED_EMPLOYEE",
    "Localized_Value": "Phone number already registered by another employee"
  },
  {
    "Localized_Key": "AUTH_EMAIL_REGISTERED_EMPLOYEE",
    "Localized_Value": "Email already registered by another employee"
  },
  {
    "Localized_Key": "AUTH_USERNAME_REGISTERED_EMPLOYEE",
    "Localized_Value": "Username already registered by another employee"
  },
  {
    "Localized_Key": "UPDATE_ACTION_NOT_SUPPORTED",
    "Localized_Value": "This update action is not supported"
  },
  {
    "Localized_Key": "PASSWORD_REQUIRED",
    "Localized_Value": "Password cannot be empty"
  },
  {
    "Localized_Key": "PASSWORD_UPDATED_SUCCESSFULLY",
    "Localized_Value": "Password updated successfully"
  },
  {
    "Localized_Key": "INVALID_DISCOUNT_VALUES",
    "Localized_Value": "Invalid discount values"
  },
  {
    "Localized_Key": "TOTAL_AFTER_TAX_CANNOT_BE_NEGATIVE",
    "Localized_Value": "Total after tax cannot be negative"
  },
  {
    "Localized_Key": "VOUCHER_MINIMUM_PURCHASE_REQUIRED",
    "Localized_Value": "Voucher yêu cầu số tiền mua tối thiểu"
  },
  {
    "Localized_Key": "TAX_RATE_CANNOT_BE_NEGATIVE",
    "Localized_Value": "Tax rate cannot be negative"
  },
  {
    "Localized_Key": "TEMPLATE_ALREADY_EXISTS",
    "Localized_Value": "This trigger event template already exists"
  },
  {
    "Localized_Key": "INVALID_PHONE_NUMBER",
    "Localized_Value": "Invalid phone number"
  },
  {
    "Localized_Key": "VOUCHER_BRANCH_DISTRIBUTION_REQUIRED",
    "Localized_Value": "Branch distribution is required when branch specific is enabled"
  },
  {
    "Localized_Key": "VOUCHER_BRANCH_DISTRIBUTION_NOT_ALLOWED",
    "Localized_Value": "Branch distribution is not allowed when branch specific is disabled"
  },
  {
    "Localized_Key": "VOUCHER_NAME_REQUIRED",
    "Localized_Value": "Voucher name is required"
  },
  {
    "Localized_Key": "VOUCHER_DATE_REQUIRED",
    "Localized_Value": "Start date and end date are required for non-long term vouchers"
  },
  {
    "Localized_Key": "VOUCHER_DATE_INVALID",
    "Localized_Value": "Start date must be before end date"
  },
  {
    "Localized_Key": "VOUCHER_PERCENT_INVALID",
    "Localized_Value": "Discount percentage must be between 0 and 100"
  },
  {
    "Localized_Key": "VOUCHER_MAX_DISCOUNT_REQUIRED",
    "Localized_Value": "Maximum discount amount is required for percentage discount"
  },
  {
    "Localized_Key": "VOUCHER_MONEY_DISCOUNT_REQUIRED",
    "Localized_Value": "Money discount amount is required"
  },
  {
    "Localized_Key": "VOUCHER_DISCOUNT_TYPE_INVALID",
    "Localized_Value": "Invalid discount type"
  },
  {
    "Localized_Key": "VOUCHER_CATEGORY_REQUIRED",
    "Localized_Value": "Category is required for category limit type"
  },
  {
    "Localized_Key": "VOUCHER_PRODUCT_REQUIRED",
    "Localized_Value": "Products are required for product limit type"
  },
  {
    "Localized_Key": "VOUCHER_MIN_ORDER_REQUIRED",
    "Localized_Value": "Minimum order amount is required for minimum money limit type"
  },
  {
    "Localized_Key": "VOUCHER_GROUP_REQUIRED",
    "Localized_Value": "User group is required for group condition type"
  },
  {
    "Localized_Key": "VOUCHER_USERS_REQUIRED",
    "Localized_Value": "Users are required for customer condition type"
  },
  {
    "Localized_Key": "VOUCHER_TRANSPORT_MONEY_REQUIRED",
    "Localized_Value": "Transport vouchers must use money discount type"
  },
  {
    "Localized_Key": "VOUCHER_SHIPPING_DISCOUNT_TYPE_REQUIRED",
    "Localized_Value": "Shipping discount type is required for transport vouchers"
  },
  {
    "Localized_Key": "VOUCHER_TRANSPORT_LIMIT_INVALID",
    "Localized_Value": "Transport vouchers cannot have category or product limits"
  },
  {
    "Localized_Key": "VOUCHER_POINT_REWARD_REQUIRED",
    "Localized_Value": "Point reward range is required for point reward type"
  },
  {
    "Localized_Key": "VOUCHER_POINT_RANGE_INVALID",
    "Localized_Value": "Minimum point reward must be less than maximum point reward"
  },
  {
    "Localized_Key": "VOUCHER_GIFT_REQUIRED",
    "Localized_Value": "Please select a gift"
  },
  {
    "Localized_Key": "VOUCHER_REWARD_GIFT_IDS_REQUIRED",
    "Localized_Value": "Please select at least one gift"
  },
  {
    "Localized_Key": "VOUCHER_REWARD_TYPE_INVALID",
    "Localized_Value": "Invalid reward type"
  },
  {
    "Localized_Key": "VOUCHER_CODE_REQUIRED",
    "Localized_Value": "Voucher code is required for common code type"
  },
  {
    "Localized_Key": "VOUCHER_CODE_EXISTS",
    "Localized_Value": "Voucher code already exists"
  },
  {
    "Localized_Key": "VOUCHER_BRANCH_QUANTITY_EXCEEDED",
    "Localized_Value": "Total branch quantity exceeds voucher quantity"
  },
  {
    "Localized_Key": "BRANCH_NOT_BELONG_TO_SHOP",
    "Localized_Value": "Branch does not belong to the shop"
  },
  {
    "Localized_Key": "VOUCHER_INACTIVE",
    "Localized_Value": "Voucher is inactive"
  },
  {
    "Localized_Key": "VOUCHER_OUT_OF_STOCK",
    "Localized_Value": "Voucher is out of stock"
  },
  {
    "Localized_Key": "VOUCHER_GROUP_NOT_MATCH",
    "Localized_Value": "Voucher group does not match"
  },
  {
    "Localized_Key": "VOUCHER_USER_NOT_MATCH",
    "Localized_Value": "Voucher user does not match"
  },
  {
    "Localized_Key": "VOUCHER_NOT_ASSIGNED",
    "Localized_Value": "Voucher is not assigned"
  },
  {
    "Localized_Key": "VOUCHER_USAGE_LIMIT_EXCEEDED",
    "Localized_Value": "Voucher usage limit exceeded"
  },
  {
    "Localized_Key": "VOUCHER_CATEGORY_NOT_MATCH",
    "Localized_Value": "Voucher category does not match"
  },
  {
    "Localized_Key": "VOUCHER_PRODUCT_NOT_MATCH",
    "Localized_Value": "Voucher product does not match"
  },
  {
    "Localized_Key": "VOUCHER_MIN_ORDER_NOT_MATCH",
    "Localized_Value": "Order amount does not meet minimum requirement"
  },
  {
    "Localized_Key": "VOUCHER_CANNOT_DELETE_USED",
    "Localized_Value": "Cannot delete voucher that has been used"
  },
  {
    "Localized_Key": "VOUCHER_CANNOT_UPDATE_USED",
    "Localized_Value": "Cannot update voucher that has been used"
  },
  {
    "Localized_Key": "VOUCHER_CANNOT_DELETE_ACTIVE",
    "Localized_Value": "Cannot delete active voucher"
  },
  {
    "Localized_Key": "INVOICE_NOT_FOUND",
    "Localized_Value": "Invoice not found"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_FAILED",
    "Localized_Value": "Failed to delete voucher"
  },
  {
    "Localized_Key": "VOUCHERS_DELETE_FAILED",
    "Localized_Value": "Failed to delete vouchers"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_CONFIRM",
    "Localized_Value": "Are you sure you want to delete this voucher?"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_CONFIRM_USED",
    "Localized_Value": "This voucher has been used. Are you sure you want to delete it?"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_CONFIRM_ACTIVE",
    "Localized_Value": "This voucher is still active. Are you sure you want to delete it?"
  },
  {
    "Localized_Key": "VOUCHER_DELETE_CANCELLED",
    "Localized_Value": "Voucher deletion cancelled"
  },
  {
    "Localized_Key": "VOUCHER_CODE_PREFIX_REQUIRED",
    "Localized_Value": "Voucher code prefix is required"
  },
  {
    "Localized_Key": "VOUCHER_CODE_PREFIX_TOO_LONG",
    "Localized_Value": "Voucher code prefix is too long"
  },
  {
    "Localized_Key": "VOUCHER_CODE_PREFIX_INVALID",
    "Localized_Value": "Voucher code prefix is invalid"
  },
  {
    "Localized_Key": "VOUCHER_NO_REMAINING_USES",
    "Localized_Value": "No remaining uses for this voucher"
  },
  {
    "Localized_Key": "VOUCHER_POINT_REWARD_INVALID",
    "Localized_Value": "Point reward is invalid"
  },
  {
    "Localized_Key": "VOUCHER_POINT_RANGE_REQUIRED",
    "Localized_Value": "Point reward range is required"
  },
  {
    "Localized_Key": "VOUCHER_POINT_RANGE_INVALID",
    "Localized_Value": "Minimum point reward must be less than maximum point reward"
  },
  {
    "Localized_Key": "VOUCHER_REWARD_GIFT_IDS_REQUIRED",
    "Localized_Value": "Reward gift is required"
  },
  {
    "Localized_Key": "VOUCHER_SHIPPING_DISCOUNT_TYPE_REQUIRED",
    "Localized_Value": "Shipping discount type is required"
  },
  {
    "Localized_Key": "VOUCHER_TRANSPORT_MONEY_REQUIRED",
    "Localized_Value": "Transport voucher must use money discount type"
  },
  {
    "Localized_Key": "VOUCHER_TRANSPORT_LIMIT_INVALID",
    "Localized_Value": "Transport voucher cannot have category or product limits"
  },
  {
    "Localized_Key": "VOUCHER_BRANCH_QUANTITY_EXCEEDED",
    "Localized_Value": "Total branch quantity exceeds voucher quantity"
  },
  {
    "Localized_Key": "VOUCHER_TYPE_CANNOT_UPDATE",
    "Localized_Value": "Cannot change voucher type after creation"
  },
  {
    "Localized_Key": "VOUCHER_DISTRIBUTION_TYPE_CANNOT_UPDATE",
    "Localized_Value": "Cannot change distribution type after creation"
  },
  {
    "Localized_Key": "VOUCHER_USER_EXCEED_QUANTITY",
    "Localized_Value": "Number of users exceeds number of issued vouchers"
  },
  {
    "Localized_Key": "VOUCHER_QUANTITY_CANNOT_LESS_THAN_DISTRIBUTED",
    "Localized_Value": "Voucher quantity cannot be less than the number already distributed to users"
  },
  // PriceList
  {
    "Localized_Key": "ITEM_ALREADY_EXISTS_IN_PRICE_LIST",
    "Localized_Value": "The product/service already exists in another price list"
  },
  {
    "Localized_Key": "RANK_ALREADY_EXISTS_IN_PRICE_LIST",
    "Localized_Value": "The customer rank already exists in another price list"
  },
  {
    "Localized_Key": "ZALO_MINIAPP_INVALID",
    "Localized_Value": "MiniAppId is invalid or does not exist on Zalo"
  },
  {
    "Localized_Key": "ZALO_TABLE_VALUE_INVALID",
    "Localized_Value": "Table value is invalid or does not exist"
  },
  {
    "Localized_Key": "MINI_APP_NOT_FOUND_OR_NOT_PARTNER",
    "Localized_Value": "Mini App does not exist or is not a partner of the system"
  },
  {
    "Localized_Key": "CATEGORY_HAS_ITEMS",
    "Localized_Value": "is currently in use in products/services and cannot be deleted."
  },
  {
    "Localized_Key": "AUTH_CONFIRM_ZALO_TOKEN_NOT_FOUND",
    "Localized_Value": "Zalo token not found for the shop"
  },
  {
    "Localized_Key": "RERUN_TIME_LIMIT_NOT_REACHED",
    "Localized_Value": "Not enough time has passed to perform this action again."
  },
  {
    "Localized_Key": "MEMBERSHIP_LEVEL_ALREADY_EXISTS_IN_PRICE_LIST",
    "Localized_Value": "Membership level already exists in the price list"
  },
  {
    "Localized_Key": "PRODUCT_ALREADY_EXISTS_IN_PRICE_LIST",
    "Localized_Value": "The product is currently being used in the price list and cannot be deleted"
  }
]
