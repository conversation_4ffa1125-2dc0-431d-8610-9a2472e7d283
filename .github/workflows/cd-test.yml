name: Deploy .NET Testing

on:
  push:
    branches:
      - "develop"
      - "test_*"
  workflow_dispatch:

env:
  app_solution: "${{secrets.APP_SOLUTION}}"
  app_image: "${{secrets.DOCKER_HUB_IMAGE}}"
  app_docker_name: "${{secrets.DOCKER_HUB_USERNAME}}"
  app_docker_pass: "${{secrets.DOCKER_HUB_PASSWORD}}"
  app_docker_container: "${{secrets.DOCKER_HUB_CONTAINER}}"
  app_remote_host: "${{secrets.TEST_REMOTE_HOST}}"
  app_remote_user: "${{secrets.TEST_REMOTE_USER}}"
  app_remote_password: "${{secrets.TEST_REMOTE_PASSWORD}}"
  app_remote_service: "${{secrets.STAG_REMOTE_SERVICE}}"
  app_remote_dir: "${{secrets.STAG_REMOTE_DIR}}"
  app_remote_build_dir: "${{secrets.STAG_REMOTE_BUILD_DIR}}"

jobs:
  build:
    runs-on: [self-hosted, dev]
    # Add outputs to pass data to telegram job
    outputs:
      deployment_status: ${{ steps.deploy.outputs.deployment_status }}
      deployment_message: ${{ steps.deploy.outputs.deployment_message }}
    steps:
      - uses: actions/checkout@v4
      - name: Extract version from tag
        run: |
          echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV
      - name: Build and Deploy (Blue-Green)
        id: deploy
        run: |
          echo $PWD
          jq '.ASPNETCORE_ENVIRONMENT = "Testing"' env.json > temp.json && mv temp.json env.json
          docker build --platform=linux/amd64 -t retail-backend .
          
          # Step 1: Start backend2 (Green) with new image
          echo "=== Starting backend2 (Green deployment) ==="
          docker stop backend2 || true
          docker rm backend2 || true
          
          echo "Starting new backend2 container..."
          docker run -d --name backend2 -p 5078:5077 --restart on-failure:5 retail-backend
          
          # Step 2: Health check backend2 for 120 seconds
          echo "Health checking backend2..."
          start_time=$(date +%s)
          backend2_healthy=false
          
          while true; do
            current_time=$(date +%s)
            elapsed=$((current_time - start_time))
            
            # Check if 120 seconds timeout
            if [ $elapsed -gt 120 ]; then
              echo "Backend2 health check timeout after 120 seconds"
              echo "Final attempt:"
              curl_result=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5078/minimumversion || echo "000")
              echo "Final backend2 curl result: $curl_result"
              break
            fi
            
            # Check if container is still running
            if ! docker ps --format '{{.Names}}' | grep -q "^backend2$"; then
              echo "Backend2 container is not running anymore!"
              echo "Container logs:"
              docker logs backend2 --tail 20 || true
              break
            fi
            
            # Manual health check using curl
            echo "Checking backend2 health... (${elapsed}s elapsed)"
            http_code=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5078/minimumversion || echo "000")
            
            if [ "$http_code" = "200" ]; then
              echo "Backend2 is healthy! (HTTP 200)"
              backend2_healthy=true
              break
            else
              echo "Backend2 health check failed - HTTP code: $http_code"
            fi
            
            # Show progress every 15 seconds
            if [ $((elapsed % 15)) -eq 0 ] && [ $elapsed -gt 0 ]; then
              echo "=== Backend2 progress at ${elapsed}s ==="
              echo "Container status: $(docker ps --format '{{.Status}}' --filter name=backend2)"
              echo "Recent logs:"
              docker logs backend2 --tail 3 2>/dev/null || echo "No logs available"
              echo "================================"
            fi
            
            sleep 5
          done
          
          # Step 3: If backend2 healthy, proceed with backend (Blue) replacement
          if [ "$backend2_healthy" = "true" ]; then
            echo "=== Backend2 healthy, replacing backend (Blue) ==="
            
            # Stop and remove old backend
            echo "Stopping and removing old backend..."
            docker stop backend || true
            docker rm backend || true
            
            # Start new backend with new image
            echo "Starting new backend container..."
            docker run -d --name backend -p 5077:5077 --restart on-failure:5 retail-backend
            
            # Step 4: Health check new backend for 120 seconds
            echo "Health checking new backend..."
            start_time=$(date +%s)
            backend_healthy=false
            
            while true; do
              current_time=$(date +%s)
              elapsed=$((current_time - start_time))
              
              # Check if 120 seconds timeout
              if [ $elapsed -gt 120 ]; then
                echo "Backend health check timeout after 120 seconds"
                echo "Final attempt:"
                curl_result=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5077/minimumversion || echo "000")
                echo "Final backend curl result: $curl_result"
                break
              fi
              
              # Check if container is still running
              if ! docker ps --format '{{.Names}}' | grep -q "^backend$"; then
                echo "Backend container is not running anymore!"
                echo "Container logs:"
                docker logs backend --tail 20 || true
                break
              fi
              
              # Manual health check using curl
              echo "Checking backend health... (${elapsed}s elapsed)"
              http_code=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5077/minimumversion || echo "000")
              
              if [ "$http_code" = "200" ]; then
                echo "Backend is healthy! (HTTP 200)"
                backend_healthy=true
                break
              else
                echo "Backend health check failed - HTTP code: $http_code"
              fi
              
              # Show progress every 15 seconds
              if [ $((elapsed % 15)) -eq 0 ] && [ $elapsed -gt 0 ]; then
                echo "=== Backend progress at ${elapsed}s ==="
                echo "Container status: $(docker ps --format '{{.Status}}' --filter name=backend)"
                echo "Recent logs:"
                docker logs backend --tail 3 2>/dev/null || echo "No logs available"
                echo "================================"
              fi
              
              sleep 5
            done
            
            # Step 5: If backend healthy, stop backend2 (cleanup)
            if [ "$backend_healthy" = "true" ]; then
              echo "=== Backend healthy, cleaning up backend2 ==="
              docker stop backend2 || true
              docker rm backend2 || true
              
              echo "deployment_status=success" >> $GITHUB_OUTPUT
              echo "deployment_message=✅ Blue-Green deployment successful" >> $GITHUB_OUTPUT
            else
              echo "=== Backend unhealthy, keeping backend2 as backup ==="
              echo "deployment_status=partial" >> $GITHUB_OUTPUT
              echo "deployment_message=⚠️ Backend failed, backend2 running as backup" >> $GITHUB_OUTPUT
            fi
            
          else
            echo "Backend2 health check failed - deployment aborted"
            echo "Container logs:"
            docker logs backend2 --tail 50 || true
            echo "Container status:"
            docker ps --filter name=backend2 || true
            
            # Stop failed backend2
            docker stop backend2 || true
            docker rm backend2 || true

            echo "deployment_status=failed" >> $GITHUB_OUTPUT
            echo "deployment_message=❌ Backend2 deployment failed, keeping current backend" >> $GITHUB_OUTPUT
          fi

          docker builder prune -a -f
          docker image prune -a -f
        working-directory: ./App.ECommerce

  send-telegram-noti:
    needs: build
    runs-on: [self-hosted, dev]
    if: always()
    steps:
      - name: get timestamp
        run: echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
      
      - name: Send success notification
        if: needs.build.outputs.deployment_status == 'success'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] 🚀 BE DEV Success
          
          ${{ needs.build.outputs.deployment_message }}
          
          🔸 Primary: http://localhost:5077
          🔸 Backup: Cleaned up (deployment complete)
          
          📝 Commit: ${{ github.event.head_commit.message }}'

      - name: Send partial success notification  
        if: needs.build.outputs.deployment_status == 'partial'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] ⚠️ BE DEV Partial Success
          
          ${{ needs.build.outputs.deployment_message }}
          
          ❌ Primary: http://localhost:5077 (failed)
          ✅ Backup: http://localhost:5078 (running)
          
          📝 Commit: ${{ github.event.head_commit.message }}'

      - name: Send failure notification
        if: needs.build.outputs.deployment_status == 'failed'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] 🚨 BE DEV Failed
          
          ${{ needs.build.outputs.deployment_message }}
          
          ❌ New version failed to deploy
          ✅ Current backend still running: http://localhost:5077
          
          📝 Commit: ${{ github.event.head_commit.message }}'

      - name: Send build failure notification
        if: needs.build.result == 'failure' && needs.build.outputs.deployment_status != 'failed'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] 💥 BE DEV Build Failed - ${{ github.event.head_commit.message }}'